"""GPU-accelerated heatmap layer for risk visualization.

This module provides heatmap rendering capabilities for visualizing
ergonomic risk intensity across the pose estimation area.
"""

import logging
import os
import time
from typing import Optional
import numpy as np
import flet as ft
from PIL import Image, ImageDraw
import io
import base64
from .encoding import _encode_frame

# Try to import OpenCV for vectorized operations
try:
    import cv2
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    logging.getLogger(__name__).warning("OpenCV not available, falling back to slow pixel-by-pixel processing")

logger = logging.getLogger(__name__)


class HeatmapLayer:
    """GPU-accelerated heatmap layer for risk visualization.
    
    Renders intensity maps with multiply blend mode for overlay visualization.
    Includes placeholder for future custom shader implementation.
    
    Attributes:
        overlay_control: Flet Image control for overlay rendering
        width: Layer width in pixels
        height: Layer height in pixels
        enabled: Whether heatmap is currently enabled
    """
    
    def __init__(self, overlay_control: ft.Image, width: int = 640, height: int = 480, test_mode: bool = False):
        """Initialize heatmap layer with overlay control and dimensions.
        
        Args:
            overlay_control: Flet Image control for overlay display
            width: Layer width in pixels
            height: Layer height in pixels
            test_mode: If True, skip expensive operations for testing
        """
        self.overlay_control = overlay_control
        self.width = width
        self.height = height
        self.test_mode = test_mode
        self.enabled = False
        self.current_intensity_map: Optional[np.ndarray] = None
        
        logger.info(f"HeatmapLayer initialized: {width}x{height}, test_mode={test_mode}")
    
    def set_enabled(self, enabled: bool) -> None:
        """Enable or disable heatmap rendering.
        
        Args:
            enabled: Whether to enable heatmap rendering
        """
        self.enabled = enabled
        if not enabled:
            self.clear_heatmap()
        logger.debug(f"Heatmap enabled: {enabled}")
    
    def update_intensity_map(self, intensity_map: np.ndarray) -> bool:
        """Update heatmap with new intensity data.
        
        Args:
            intensity_map: 2D array of intensity values [0.0, 1.0]
                          Shape should match (height, width)
        
        Returns:
            True if heatmap was successfully updated, False otherwise
        """
        try:
            if not self.enabled:
                return False
            
            if intensity_map is None or intensity_map.size == 0:
                logger.warning("Empty intensity map provided")
                return False
            
            # Validate and resize intensity map if needed
            if len(intensity_map.shape) != 2:
                logger.error("Intensity map must be 2D array")
                return False
            
            # Resize to match layer dimensions
            if intensity_map.shape != (self.height, self.width):
                import cv2
                intensity_map = cv2.resize(intensity_map, (self.width, self.height))
            
            self.current_intensity_map = intensity_map
            return self._render_heatmap(intensity_map)
            
        except Exception as e:
            logger.error(f"Failed to update intensity map: {e}")
            return False
    
    def render_stub(self) -> bool:
        """Render stub translucent red overlay for testing.
        
        Provides a simple red overlay to verify heatmap functionality
        before implementing full intensity mapping.
        
        Returns:
            True if stub was successfully rendered, False otherwise
        """
        try:
            if not self.overlay_control:
                return False
            
            # Fast path for testing - skip expensive operations
            if self.test_mode:
                logger.debug("HeatmapLayer.render_stub skipped (test_mode)")
                return True
            
            if not self.enabled:
                return False
            
            # Create translucent red overlay image
            overlay_image = Image.new('RGBA', (self.width, self.height), (255, 100, 100, 76))  # Red with ~30% opacity
            
            # Convert to base64 using shared encoder
            img_base64 = _encode_frame(overlay_image, self.test_mode)
            if img_base64 is None:
                logger.error("Failed to encode heatmap stub")
                return False
            
            # Update overlay control
            self.overlay_control.src_base64 = img_base64
            
            logger.debug("Stub heatmap overlay rendered")
            return True
            
        except Exception as e:
            logger.error(f"Failed to render stub heatmap: {e}")
            return False
    
    def _render_heatmap(self, intensity_map: np.ndarray) -> bool:
        """Render actual heatmap from intensity data.
        
        Args:
            intensity_map: 2D intensity array [0.0, 1.0]
        
        Returns:
            True if heatmap was successfully rendered, False otherwise
        """
        try:
            # Create color-mapped heatmap using PIL
            heatmap_image = self._create_heatmap_image(intensity_map)
            if heatmap_image is None:
                return False
            
            # Convert to base64 using shared encoder
            img_base64 = _encode_frame(heatmap_image, self.test_mode)
            if img_base64 is None:
                logger.error("Failed to encode heatmap image")
                return False
            
            # Create image rectangle for overlay
            # Update overlay control with heatmap image
            # TODO-CODE: Implement proper multiply blend mode when Flet supports it
            self.overlay_control.src_base64 = img_base64
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to render heatmap: {e}")
            return False
    
    def _create_heatmap_image(self, intensity_map: np.ndarray) -> Optional[Image.Image]:
        """Create PIL image from intensity map with color mapping.
        
        Args:
            intensity_map: 2D intensity array [0.0, 1.0]
        
        Returns:
            PIL Image with color-mapped heatmap or None if failed
        """
        perf_logging = os.getenv("GPU_PERF_METRICS") == "1"
        start_time = time.perf_counter() if perf_logging else None
        
        try:
            # Fast vectorized path using OpenCV
            if CV2_AVAILABLE:
                return self._create_heatmap_vectorized(intensity_map, perf_logging, start_time)
            else:
                # Fallback to original pixel-by-pixel method
                return self._create_heatmap_legacy(intensity_map, perf_logging, start_time)
            
        except Exception as e:
            logger.error(f"Failed to create heatmap image: {e}")
            return None
    
    def _create_heatmap_vectorized(self, intensity_map: np.ndarray, perf_logging: bool, start_time: Optional[float]) -> Optional[Image.Image]:
        """Vectorized heatmap creation using OpenCV (target ≤10ms).
        
        Args:
            intensity_map: 2D intensity array [0.0, 1.0]
            perf_logging: Whether to log performance metrics
            start_time: Performance counter start time
        
        Returns:
            PIL Image with color-mapped heatmap or None if failed
        """
        try:
            # Normalize intensity to 0-255 range for OpenCV
            norm = np.clip((intensity_map * 255).astype(np.uint8), 0, 255)
            
            # Apply color map using OpenCV (JET colormap: blue->cyan->yellow->red)
            colored = cv2.applyColorMap(norm, cv2.COLORMAP_JET)  # Returns BGR format
            
            # Convert BGR to RGBA
            rgba = cv2.cvtColor(colored, cv2.COLOR_BGR2RGBA)
            
            # Set alpha channel based on intensity (more transparent at low values)
            rgba[..., 3] = (intensity_map * 255 * 1.5).clip(0, 255).astype(np.uint8)
            
            if perf_logging and start_time is not None:
                heatmap_time = (time.perf_counter() - start_time) * 1000
                logger.debug(f"Vectorized heatmap creation took {heatmap_time:.2f}ms")
            
            return Image.fromarray(rgba, mode='RGBA')
            
        except Exception as e:
            logger.error(f"Vectorized heatmap creation failed: {e}")
            return None
    
    def _create_heatmap_legacy(self, intensity_map: np.ndarray, perf_logging: bool, start_time: Optional[float]) -> Optional[Image.Image]:
        """Legacy pixel-by-pixel heatmap creation (fallback when OpenCV unavailable).
        
        Args:
            intensity_map: 2D intensity array [0.0, 1.0]
            perf_logging: Whether to log performance metrics
            start_time: Performance counter start time
        
        Returns:
            PIL Image with color-mapped heatmap or None if failed
        """
        try:
            # Normalize intensity to 0-255 range
            intensity_normalized = (np.clip(intensity_map, 0, 1) * 255).astype(np.uint8)
            
            # Create color map: Blue (low) -> Green -> Yellow -> Red (high)
            colored_map = np.zeros((intensity_map.shape[0], intensity_map.shape[1], 4), dtype=np.uint8)
            
            # Color mapping logic (original implementation)
            for i in range(intensity_map.shape[0]):
                for j in range(intensity_map.shape[1]):
                    intensity = intensity_normalized[i, j]
                    alpha = min(255, int(intensity * 1.5))  # Make more transparent at low values
                    
                    if intensity < 64:  # Blue zone (low risk)
                        colored_map[i, j] = [0, 0, 255, alpha]
                    elif intensity < 128:  # Green zone
                        colored_map[i, j] = [0, 255, 0, alpha]
                    elif intensity < 192:  # Yellow zone
                        colored_map[i, j] = [255, 255, 0, alpha]
                    else:  # Red zone (high risk)
                        colored_map[i, j] = [255, 0, 0, alpha]
            
            if perf_logging and start_time is not None:
                heatmap_time = (time.perf_counter() - start_time) * 1000
                logger.debug(f"Legacy heatmap creation took {heatmap_time:.2f}ms")
            
            return Image.fromarray(colored_map, mode='RGBA')
            
        except Exception as e:
            logger.error(f"Legacy heatmap creation failed: {e}")
            return None
    
    def _clear_heatmap_shapes(self) -> None:
        """Clear heatmap-specific shapes from canvas."""
        if self.canvas and hasattr(self.canvas, 'shapes'):
            # Remove only heatmap shapes (keeping skeleton, etc.)
            # For simplicity, we'll clear all for now
            # TODO-CODE: Implement shape tagging for selective clearing
            pass
    
    def clear_heatmap(self) -> None:
        """Clear all heatmap rendering from overlay."""
        try:
            if self.overlay_control:
                self.overlay_control.src_base64 = None
            self.current_intensity_map = None
            logger.debug("Heatmap cleared from overlay")
        except Exception as e:
            logger.error(f"Failed to clear heatmap: {e}")


# TODO-CODE: Future GPU shader implementation
# class GPUShaderHeatmap:
#     """Custom OpenGL/Vulkan shader-based heatmap renderer.
#     
#     Will implement:
#     - Real-time fragment shader for intensity -> color mapping
#     - Hardware-accelerated multiply blend mode
#     - Gaussian blur for smooth intensity transitions
#     - Multi-layer heatmap composition (risk zones, attention areas)
#     
#     Shader pipeline:
#     1. Vertex shader: Transform quad vertices
#     2. Fragment shader: Sample intensity texture, apply color LUT
#     3. Blend: Multiply with underlying video/skeleton
#     """
#     
#     def __init__(self, gl_context):
#         pass
#     
#     def compile_shaders(self):
#         # Vertex shader for quad rendering
#         vertex_shader = """
#         #version 330 core
#         layout (location = 0) in vec3 position;
#         layout (location = 1) in vec2 texCoord;
#         
#         out vec2 TexCoord;
#         
#         void main() {
#             gl_Position = vec4(position, 1.0);
#             TexCoord = texCoord;
#         }
#         """
#         
#         # Fragment shader for intensity -> color mapping
#         fragment_shader = """
#         #version 330 core
#         in vec2 TexCoord;
#         out vec4 FragColor;
#         
#         uniform sampler2D intensityTexture;
#         uniform sampler1D colorLUT;
#         uniform float opacity;
#         
#         void main() {
#             float intensity = texture(intensityTexture, TexCoord).r;
#             vec3 color = texture(colorLUT, intensity).rgb;
#             FragColor = vec4(color, intensity * opacity);
#         }
#         """
#         pass