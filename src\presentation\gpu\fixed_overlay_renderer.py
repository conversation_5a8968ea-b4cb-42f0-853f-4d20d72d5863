"""Fixed Overlay Renderer - Solusi untuk masalah overlay hitam/kosong

Module ini menyediakan renderer overlay yang diperbaiki untuk mengatasi masalah:
- Overlay skeleton yang hanya menampilkan garis hitam
- Heatmap overlay yang tidak terlihat
- Masalah transparansi RGBA
- Encoding base64 yang tidak konsisten
"""

import logging
import time
import threading
from typing import Optional, Dict, Any, Tuple
import numpy as np
import base64
import io

from PIL import Image, ImageDraw
import flet as ft

logger = logging.getLogger(__name__)


class FixedOverlayRenderer:
    """Improved overlay renderer with proper transparency and encoding."""
    
    def __init__(self, overlay_control: ft.Image, width: int = 640, height: int = 480, 
                 test_mode: bool = False):
        """Initialize fixed overlay renderer.
        
        Args:
            overlay_control: Flet Image control for overlay display
            width: Overlay width in pixels
            height: Overlay height in pixels
            test_mode: Enable test mode for debugging
        """
        self.overlay_control = overlay_control
        self.width = width
        self.height = height
        self.test_mode = test_mode
        self.enabled = True
        
        # Performance tracking
        self._render_count = 0
        self._last_render_time = 0
        self._render_lock = threading.Lock()
        
        # Cache for performance
        self._cached_overlay = None
        self._last_keypoints_hash = None
        self._last_risk_level = None
        
        # Risk level colors with proper alpha
        self.RISK_COLORS = {
            'low': (0, 255, 0, 180),      # Green with transparency
            'medium': (255, 255, 0, 180), # Yellow with transparency  
            'high': (255, 165, 0, 180),   # Orange with transparency
            'critical': (255, 0, 0, 180), # Red with transparency
            'unknown': (128, 128, 128, 180) # Gray with transparency
        }
        
        # Skeleton connections for human pose
        self.SKELETON_CONNECTIONS = [
            # Head and torso
            ('nose', 'left_shoulder'),
            ('nose', 'right_shoulder'),
            ('left_shoulder', 'right_shoulder'),
            ('left_shoulder', 'left_hip'),
            ('right_shoulder', 'right_hip'),
            ('left_hip', 'right_hip'),
            
            # Arms
            ('left_shoulder', 'left_elbow'),
            ('left_elbow', 'left_wrist'),
            ('right_shoulder', 'right_elbow'),
            ('right_elbow', 'right_wrist'),
            
            # Legs
            ('left_hip', 'left_knee'),
            ('left_knee', 'left_ankle'),
            ('right_hip', 'right_knee'),
            ('right_knee', 'right_ankle'),
        ]
        
        logger.info(f"FixedOverlayRenderer initialized: {width}x{height}")
    
    def set_enabled(self, enabled: bool) -> None:
        """Enable or disable overlay rendering."""
        self.enabled = enabled
        if not enabled:
            self.clear_overlay()
    
    def clear_overlay(self) -> bool:
        """Clear the overlay by setting transparent image."""
        try:
            if not self.overlay_control:
                return False
            
            # Create transparent overlay
            transparent_image = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
            img_base64 = self._encode_overlay_to_base64(transparent_image)
            
            if img_base64:
                self.overlay_control.src_base64 = img_base64
                return True
            return False
            
        except Exception as e:
            logger.error(f"Failed to clear overlay: {e}")
            return False
    
    def render_skeleton(self, keypoints: Dict[str, Tuple[float, float, float]], 
                       risk_level: str = 'unknown') -> bool:
        """Render skeleton overlay with improved transparency and colors.
        
        Args:
            keypoints: Dictionary of keypoint names to (x, y, confidence) tuples
            risk_level: Risk level for color coding
            
        Returns:
            True if skeleton was successfully rendered, False otherwise
        """
        try:
            if not keypoints or not self.overlay_control or not self.enabled:
                return False
            
            # Fast path for testing
            if self.test_mode:
                return True
            
            # Performance optimization: check if we need to re-render
            keypoints_hash = hash(tuple(sorted(keypoints.items())))
            if (self._last_keypoints_hash == keypoints_hash and
                self._last_risk_level == risk_level and
                self._cached_overlay is not None):
                # Use cached overlay if nothing changed
                self.overlay_control.src_base64 = self._cached_overlay
                return True
            
            # Non-blocking render attempt
            if not self._render_lock.acquire(blocking=False):
                return False
            
            try:
                self._render_count += 1
                
                # Create transparent RGBA overlay
                overlay_image = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
                draw = ImageDraw.Draw(overlay_image)
                
                # Get color for current risk level
                color = self.RISK_COLORS.get(risk_level, self.RISK_COLORS['unknown'])
                
                # Render skeleton connections with improved visibility
                self._render_connections_improved(draw, keypoints, color)
                
                # Render keypoints as circles
                self._render_keypoints_improved(draw, keypoints, color)
                
                # Convert to base64 with proper PNG encoding
                img_base64 = self._encode_overlay_to_base64(overlay_image)
                if img_base64 is None:
                    return False
                
                # Update overlay control and cache
                self.overlay_control.src_base64 = img_base64
                self._cached_overlay = img_base64
                self._last_keypoints_hash = keypoints_hash
                self._last_risk_level = risk_level
                
                # Log performance periodically
                if self._render_count % 100 == 0:
                    logger.debug(f"Fixed skeleton renderer: {self._render_count} renders completed")
                
                return True
                
            finally:
                self._render_lock.release()
            
        except Exception as e:
            logger.error(f"Failed to render skeleton: {e}")
            return self._render_fallback_skeleton(risk_level)
    
    def render_heatmap(self, intensity_map: Optional[np.ndarray] = None, 
                      risk_level: str = 'medium') -> bool:
        """Render heatmap overlay with improved transparency.
        
        Args:
            intensity_map: 2D numpy array of intensity values (0-1)
            risk_level: Risk level for color scheme
            
        Returns:
            True if heatmap was successfully rendered, False otherwise
        """
        try:
            if not self.overlay_control or not self.enabled:
                return False
            
            if self.test_mode:
                return True
            
            # Create heatmap image
            if intensity_map is not None:
                heatmap_image = self._create_heatmap_from_intensity(intensity_map, risk_level)
            else:
                heatmap_image = self._create_demo_heatmap(risk_level)
            
            if heatmap_image is None:
                return False
            
            # Convert to base64
            img_base64 = self._encode_overlay_to_base64(heatmap_image)
            if img_base64 is None:
                return False
            
            # Update overlay control
            self.overlay_control.src_base64 = img_base64
            return True
            
        except Exception as e:
            logger.error(f"Failed to render heatmap: {e}")
            return self._render_fallback_heatmap(risk_level)
    
    def _render_connections_improved(self, draw: ImageDraw.Draw, 
                                   keypoints: Dict[str, tuple], color: tuple) -> None:
        """Render skeleton connections with improved visibility."""
        try:
            for start_point, end_point in self.SKELETON_CONNECTIONS:
                if start_point in keypoints and end_point in keypoints:
                    start_pos = keypoints[start_point]
                    end_pos = keypoints[end_point]
                    
                    # Check confidence threshold
                    if (len(start_pos) >= 3 and len(end_pos) >= 3 and
                        start_pos[2] > 0.5 and end_pos[2] > 0.5):
                        
                        # Convert normalized coordinates to pixel coordinates
                        x1 = int(start_pos[0] * self.width)
                        y1 = int(start_pos[1] * self.height)
                        x2 = int(end_pos[0] * self.width)
                        y2 = int(end_pos[1] * self.height)
                        
                        # Draw line with outline for better visibility
                        # Draw thicker black outline first
                        draw.line([(x1, y1), (x2, y2)], fill=(0, 0, 0, 200), width=5)
                        # Draw colored line on top
                        draw.line([(x1, y1), (x2, y2)], fill=color, width=3)
                        
        except Exception as e:
            logger.debug(f"Failed to render connections: {e}")
    
    def _render_keypoints_improved(self, draw: ImageDraw.Draw, 
                                 keypoints: Dict[str, tuple], color: tuple) -> None:
        """Render keypoints with improved visibility."""
        try:
            for point_name, position in keypoints.items():
                if len(position) >= 3 and position[2] > 0.5:  # Confidence check
                    x = int(position[0] * self.width)
                    y = int(position[1] * self.height)
                    
                    # Draw circle with outline for better visibility
                    radius = 6
                    # Black outline
                    draw.ellipse([x-radius-1, y-radius-1, x+radius+1, y+radius+1], 
                               fill=(0, 0, 0, 200))
                    # Colored fill
                    draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                               fill=color)
                    # White center dot
                    draw.ellipse([x-2, y-2, x+2, y+2], 
                               fill=(255, 255, 255, 255))
                    
        except Exception as e:
            logger.debug(f"Failed to render keypoints: {e}")
    
    def _create_demo_heatmap(self, risk_level: str) -> Optional[Image.Image]:
        """Create demo heatmap for testing."""
        try:
            # Create radial gradient heatmap
            overlay = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
            
            # Create gradient effect
            center_x, center_y = self.width // 2, self.height // 2
            max_radius = min(self.width, self.height) // 3
            
            color = self.RISK_COLORS.get(risk_level, self.RISK_COLORS['unknown'])
            
            # Draw concentric circles for gradient effect
            for radius in range(max_radius, 0, -5):
                alpha = int((radius / max_radius) * color[3] * 0.3)  # Fade out
                circle_color = (color[0], color[1], color[2], alpha)
                
                draw = ImageDraw.Draw(overlay)
                draw.ellipse([center_x - radius, center_y - radius,
                            center_x + radius, center_y + radius],
                           fill=circle_color)
            
            return overlay
            
        except Exception as e:
            logger.error(f"Failed to create demo heatmap: {e}")
            return None
    
    def _create_heatmap_from_intensity(self, intensity_map: np.ndarray, 
                                     risk_level: str) -> Optional[Image.Image]:
        """Convert intensity map to colored RGBA heatmap."""
        try:
            if len(intensity_map.shape) != 2:
                logger.warning(f"Invalid intensity map shape: {intensity_map.shape}")
                return None
            
            # Normalize intensity
            intensity = np.clip(intensity_map, 0, 1)
            
            # Get base color
            color = self.RISK_COLORS.get(risk_level, self.RISK_COLORS['unknown'])
            
            # Create RGBA array
            height, width = intensity.shape
            rgba_array = np.zeros((height, width, 4), dtype=np.uint8)
            
            # Set RGB channels
            rgba_array[:, :, 0] = color[0]
            rgba_array[:, :, 1] = color[1]
            rgba_array[:, :, 2] = color[2]
            
            # Set alpha channel based on intensity
            rgba_array[:, :, 3] = (intensity * color[3]).astype(np.uint8)
            
            # Convert to PIL Image and resize
            heatmap_image = Image.fromarray(rgba_array, mode='RGBA')
            heatmap_image = heatmap_image.resize((self.width, self.height), 
                                               Image.Resampling.LANCZOS)
            
            return heatmap_image
            
        except Exception as e:
            logger.error(f"Failed to create heatmap from intensity: {e}")
            return None
    
    def _encode_overlay_to_base64(self, overlay_image: Image.Image) -> Optional[str]:
        """Encode overlay image to base64 with proper PNG transparency."""
        try:
            # Ensure RGBA mode for transparency
            if overlay_image.mode != 'RGBA':
                overlay_image = overlay_image.convert('RGBA')
            
            # Save as PNG with transparency
            buffer = io.BytesIO()
            overlay_image.save(buffer, format='PNG', optimize=True)
            
            # Encode to base64 with data URL format
            img_bytes = buffer.getvalue()
            img_base64 = base64.b64encode(img_bytes).decode('utf-8')
            
            return f"data:image/png;base64,{img_base64}"
            
        except Exception as e:
            logger.error(f"Failed to encode overlay: {e}")
            return None
    
    def _render_fallback_skeleton(self, risk_level: str) -> bool:
        """Render fallback skeleton when main rendering fails."""
        try:
            overlay = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)
            
            # Draw simple error indicator
            color = self.RISK_COLORS.get(risk_level, self.RISK_COLORS['unknown'])
            draw.rectangle([10, 10, 150, 40], fill=(255, 0, 0, 100))
            draw.text((15, 20), "Skeleton Error", fill=(255, 255, 255, 255))
            
            img_base64 = self._encode_overlay_to_base64(overlay)
            if img_base64:
                self.overlay_control.src_base64 = img_base64
                return True
            return False
            
        except Exception as e:
            logger.error(f"Fallback skeleton render failed: {e}")
            return False
    
    def _render_fallback_heatmap(self, risk_level: str) -> bool:
        """Render fallback heatmap when main rendering fails."""
        try:
            overlay = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
            draw = ImageDraw.Draw(overlay)
            
            # Draw simple error indicator
            draw.rectangle([self.width-150, 10, self.width-10, 40], 
                         fill=(255, 0, 0, 100))
            draw.text((self.width-145, 20), "Heatmap Error", fill=(255, 255, 255, 255))
            
            img_base64 = self._encode_overlay_to_base64(overlay)
            if img_base64:
                self.overlay_control.src_base64 = img_base64
                return True
            return False
            
        except Exception as e:
            logger.error(f"Fallback heatmap render failed: {e}")
            return False
