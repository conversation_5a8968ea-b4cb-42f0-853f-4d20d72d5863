2025-06-16 08:44:10,769 - __main__ - INFO - TESTING COMPLETE FIX COMPONENTS...
2025-06-16 08:44:10,776 - __main__ - INFO - Video encoding test: PASSED
2025-06-16 08:44:10,796 - __main__ - INFO - Overlay creation test: PASSED
2025-06-16 08:44:10,796 - __main__ - INFO - Starting complete fix demo...
2025-06-16 08:44:10,796 - __main__ - INFO - MEMULAI DEMO COMPLETE FIX
2025-06-16 08:44:10,796 - __main__ - INFO - ============================================================
2025-06-16 08:44:10,796 - __main__ - INFO - PERBAIKAN LENGKAP YANG DITERAPKAN:
2025-06-16 08:44:10,796 - __main__ - INFO -    1. Video feed encoding dan display
2025-06-16 08:44:10,796 - __main__ - INFO -    2. Overlay skeleton dengan transparansi RGBA
2025-06-16 08:44:10,796 - __main__ - INFO -    3. Heatmap overlay dengan gradient
2025-06-16 08:44:10,796 - __main__ - INFO -    4. Layer stacking order yang benar
2025-06-16 08:44:10,796 - __main__ - INFO -    5. Error handling dan fallback mechanisms
2025-06-16 08:44:10,796 - __main__ - INFO -    6. High quality encoding untuk semua komponen
2025-06-16 08:44:10,796 - __main__ - INFO - ============================================================
2025-06-16 08:44:10,845 - __main__ - INFO - VideoSurface patched with complete fix
2025-06-16 08:44:10,845 - __main__ - INFO - Overlay renderers patched with complete fix
2025-06-16 08:44:15,694 - __main__ - INFO - Main app video display patched with complete fix
2025-06-16 08:44:15,697 - __main__ - INFO - MELUNCURKAN APLIKASI DENGAN COMPLETE FIX...
2025-06-16 08:44:15,697 - __main__ - INFO -    Video: High quality JPEG encoding (95%)
2025-06-16 08:44:15,697 - __main__ - INFO -    Overlay: RGBA PNG dengan transparansi
2025-06-16 08:44:15,697 - __main__ - INFO -    Stacking: Video base + overlay layers
2025-06-16 08:44:15,697 - __main__ - INFO -    Debug: Enabled untuk troubleshooting
2025-06-16 08:44:15,697 - __main__ - INFO - 
2025-06-16 08:44:15,697 - flet - INFO - Assets path configured: C:\Users\<USER>\Downloads\tubes bio\assets
2025-06-16 08:44:15,697 - flet - INFO - Starting up TCP server on localhost:57354
2025-06-16 08:44:15,709 - flet - INFO - Flet app has started...
2025-06-16 08:44:15,709 - flet - INFO - App URL: tcp://localhost:57354
2025-06-16 08:44:15,709 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 08:44:15,709 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\Downloads\tubes bio\biogacorwell2\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 08:44:15,709 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\Downloads\tubes bio\biogacorwell2\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 08:44:15,903 - flet - INFO - App session started
2025-06-16 08:44:18,090 - src.infrastructure.camera.windows_camera_adapter - INFO - Camera initialized: 640x480 @ 30.00003000003fps
2025-06-16 08:44:18,508 - src.presentation.__main__ - INFO - Camera adapter initialized successfully
2025-06-16 08:44:18,508 - src.infrastructure.pose.movenet_engine - INFO - MoveNet model not found at movenet_thunder_float16.tflite
2025-06-16 08:44:18,509 - src.infrastructure.pose.movenet_engine - INFO - TODO: Download model from TFHub - implement in production
2025-06-16 08:44:18,509 - src.presentation.__main__ - WARNING - MoveNet initialization failed, trying MediaPipe
2025-06-16 08:44:18,565 - src.infrastructure.pose.mediapipe_engine - INFO - MediaPipe engine initialized (complexity=1)
2025-06-16 08:44:18,565 - src.application.use_cases.session_management - INFO - Session management use case initialized
2025-06-16 08:44:18,566 - src.domain.recommendation.rule_engine - INFO - Rule engine initialized with recommendation templates
2025-06-16 08:44:18,566 - src.domain.recommendation.fuzzy_engine - INFO - Fuzzy logic engine initialized
2025-06-16 08:44:18,566 - src.domain.recommendation.reinforcement_agent - INFO - Reinforcement learning agent initialized (demo mode)
2025-06-16 08:44:18,567 - src.application.use_cases.performance_monitor - INFO - PerformanceMonitor initialized (target: 25.0 FPS)
2025-06-16 08:44:18,569 - src.presentation.ui.widgets.recommendations_panel - INFO - Recommendations panel initialized
2025-06-16 08:44:18,574 - src.presentation.ui.widgets.reporting_panel - INFO - Reporting panel initialized
2025-06-16 08:44:18,591 - src.presentation.gpu.video_surface - INFO - VideoSurface initialized: 640x480, test_mode=False
2025-06-16 08:44:18,591 - __main__ - INFO - CompleteFixedVideoSurface initialized: 640x480
2025-06-16 08:44:18,591 - __main__ - INFO - CompleteFixedSkeletonRenderer initialized: 640x480
2025-06-16 08:44:18,592 - __main__ - INFO - CompleteFixedHeatmapLayer initialized: 640x480
2025-06-16 08:44:18,616 - src.presentation.__main__ - INFO - Video preview started
2025-06-16 08:44:18,616 - src.presentation.__main__ - INFO - Ergonomic assessment application started successfully
2025-06-16 08:46:04,966 - src.application.use_cases.session_management - INFO - Started new session: 079e7b65-262e-40fe-a392-6ecb10023b47 - Sesi 08:44
2025-06-16 08:46:04,967 - src.presentation.__main__ - INFO - Started session: 079e7b65-262e-40fe-a392-6ecb10023b47
2025-06-16 08:46:06,316 - src.application.use_cases.generate_recommendation - INFO - Recommendation generation service started
2025-06-16 08:46:06,628 - src.infrastructure.camera.windows_camera_adapter - WARNING - Failed to read frame from camera
2025-06-16 08:46:08,009 - src.infrastructure.camera.windows_camera_adapter - INFO - Camera initialized: 640x480 @ 30.00003000003fps
2025-06-16 08:46:08,455 - src.infrastructure.pose.mediapipe_engine - INFO - MediaPipe engine initialized (complexity=1)
2025-06-16 08:46:08,457 - src.application.use_cases.performance_monitor - INFO - Performance monitoring started
2025-06-16 08:46:08,460 - src.application.use_cases.capture_and_score - INFO - Async capture and score pipeline started at 25.0 FPS
2025-06-16 08:46:08,466 - src.presentation.__main__ - INFO - Real-time assessment started
2025-06-16 08:46:08,514 - __main__ - INFO - Video frame 1 updated, size: 89475
2025-06-16 08:46:08,645 - __main__ - INFO - Video frame 2 updated, size: 88731
2025-06-16 08:46:08,751 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:08,787 - __main__ - INFO - Video frame 3 updated, size: 86263
2025-06-16 08:46:08,817 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:08,919 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:08,979 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,046 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,108 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,173 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,305 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,461 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,467 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 6.1 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:46:09,532 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,626 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,697 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,820 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:09,914 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,009 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,097 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,149 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,402 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,494 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 7.2 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:46:10,511 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,613 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,661 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,707 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,769 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,839 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:10,983 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,087 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,216 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,361 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,486 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,546 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,556 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 6.4 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:46:11,664 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,727 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,864 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:11,967 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,039 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,079 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,123 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,286 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,329 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,442 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,534 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,578 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 8.8 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:46:12,611 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,679 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,732 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,883 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:12,929 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,015 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,104 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,164 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,256 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,314 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,454 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,577 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,593 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 8.3 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:46:13,718 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,770 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:13,894 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,039 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,198 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,296 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,436 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,585 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,616 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 6.8 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:46:14,696 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,786 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,925 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:14,992 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:15,098 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:15,229 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:15,433 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:15,531 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:15,641 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:15,648 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 6.7 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:46:15,765 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:15,869 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:15,954 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:16,055 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:16,141 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:16,202 - src.core.event_bus - ERROR - Error in event handler <src.presentation.__main__.ScoreDisplayHandler object at 0x000001A42DDF2350>: 'CompleteFixedSkeletonRenderer' object has no attribute 'draw'
2025-06-16 08:46:16,219 - __main__ - INFO - ============================================================
2025-06-16 08:46:16,219 - __main__ - INFO - COMPLETE FIX DEMO COMPLETED
2025-06-16 08:46:16,220 - __main__ - INFO - Video feed dan overlay issues should be resolved!
2025-06-16 08:46:16,220 - __main__ - INFO - ============================================================
