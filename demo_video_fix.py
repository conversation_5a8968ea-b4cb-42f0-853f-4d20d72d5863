"""Video Feed Fix Demo - Memperbaiki masalah video tidak tertampil

Demo ini memperbaiki masalah dimana:
- Video feed tidak tertampil (layar hitam)
- Overlay muncul tapi video base layer tidak terlihat
- Masalah layer stacking di Flet Stack
- Update mechanism yang tidak sinkron

Perbaikan yang diterapkan:
1. Memastikan video base layer diupdate dengan benar
2. Memperbaiki layer stacking order
3. Debug video feed pipeline
4. Fallback untuk video display issues
"""

import logging
import sys
import time
import threading
from pathlib import Path
from typing import Optional
import numpy as np
import base64
import io

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging for demo
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('video_fix_demo.log')
    ]
)

logger = logging.getLogger(__name__)

try:
    from PIL import Image
    import flet as ft
    import cv2
    PIL_AVAILABLE = True
    CV2_AVAILABLE = True
except ImportError as e:
    logger.error(f"Required dependencies not available: {e}")
    sys.exit(1)


def patch_video_surface():
    """Patch VideoSurface untuk memperbaiki masalah video tidak tertampil."""
    try:
        import src.presentation.gpu.video_surface as video_module
        
        # Store original class
        OriginalVideoSurface = video_module.VideoSurface
        
        class FixedVideoSurface(OriginalVideoSurface):
            """VideoSurface yang diperbaiki dengan debug dan fallback."""
            
            def __init__(self, width: int = 640, height: int = 480, test_mode: bool = False):
                super().__init__(width, height, test_mode)
                self.debug_mode = True
                self.frame_count = 0
                self.last_update_time = 0
                logger.info(f"FixedVideoSurface initialized: {width}x{height}")
            
            def get_control(self) -> ft.Control:
                """Get control dengan layer stacking yang diperbaiki."""
                if not self.image_control or not self.overlay_control:
                    raise RuntimeError("VideoSurface not properly initialized")
                
                # Pastikan video layer ada di bawah overlay
                # Set explicit z-index jika memungkinkan
                video_container = ft.Container(
                    content=self.image_control,
                    width=self.width,
                    height=self.height,
                    bgcolor=ft.Colors.BLACK,  # Background hitam untuk debug
                )
                
                overlay_container = ft.Container(
                    content=self.overlay_control,
                    width=self.width,
                    height=self.height,
                    bgcolor=ft.Colors.TRANSPARENT,
                )
                
                # Stack dengan order yang jelas: video di bawah, overlay di atas
                return ft.Stack([
                    video_container,  # Base layer
                    overlay_container,  # Overlay layer
                ], width=self.width, height=self.height)
            
            def update_frame(self, frame: np.ndarray) -> bool:
                """Update frame dengan debug dan error handling yang lebih baik."""
                try:
                    if frame is None or frame.size == 0:
                        logger.warning("Empty frame provided to update_frame")
                        return False
                    
                    self.frame_count += 1
                    current_time = time.time()
                    
                    # Debug info setiap 30 frame
                    if self.frame_count % 30 == 0:
                        fps = 30 / (current_time - self.last_update_time) if self.last_update_time > 0 else 0
                        logger.info(f"Video update: frame {self.frame_count}, FPS: {fps:.1f}")
                        self.last_update_time = current_time
                    
                    # Fast path for testing
                    if self.test_mode:
                        logger.debug("VideoSurface.update_frame skipped (test_mode)")
                        return True
                    
                    # Pastikan frame dalam format yang benar
                    if len(frame.shape) != 3 or frame.shape[2] != 3:
                        logger.error(f"Invalid frame format: {frame.shape}")
                        return False
                    
                    # Resize frame jika perlu
                    if frame.shape[:2] != (self.height, self.width):
                        frame = cv2.resize(frame, (self.width, self.height))
                    
                    # Encode frame dengan kualitas tinggi
                    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]  # Kualitas tinggi
                    success, buffer = cv2.imencode('.jpg', frame, encode_params)
                    
                    if not success or buffer is None:
                        logger.error("Failed to encode frame to JPEG")
                        return False
                    
                    # Create data URL
                    img_base64 = base64.b64encode(buffer).decode('utf-8')
                    data_url = f"data:image/jpeg;base64,{img_base64}"
                    
                    # Update image control
                    if self.image_control:
                        self.image_control.src_base64 = data_url
                        
                        # Debug: log first few successful updates
                        if self.frame_count <= 5:
                            logger.info(f"Frame {self.frame_count} updated successfully, data URL length: {len(data_url)}")
                        
                        return True
                    else:
                        logger.error("Image control is None")
                        return False
                    
                except Exception as e:
                    logger.error(f"Failed to update video frame: {e}")
                    return False
            
            @staticmethod
            def from_ndarray(frame: np.ndarray, target_width: int = 640, target_height: int = 480, test_mode: bool = False) -> Optional[str]:
                """Convert numpy array dengan debug yang lebih baik."""
                try:
                    if frame is None or frame.size == 0:
                        logger.warning("Empty frame in from_ndarray")
                        return None
                    
                    if test_mode:
                        return "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAABAAEDASIAAhEBAxEB/8QAFQABAQAAAAAAAAAAAAAAAAAAAAv/xAAUEAEAAAAAAAAAAAAAAAAAAAAA/8QAFQEBAQAAAAAAAAAAAAAAAAAAAAX/xAAUEQEAAAAAAAAAAAAAAAAAAAAA/9oADAMBAAIRAxEAPwA/8A8A"
                    
                    # Resize jika perlu
                    if frame.shape[:2] != (target_height, target_width):
                        frame = cv2.resize(frame, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
                    
                    # Encode dengan kualitas tinggi
                    encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]
                    success, buffer = cv2.imencode('.jpg', frame, encode_params)
                    
                    if not success:
                        logger.error("JPEG encoding failed in from_ndarray")
                        return None
                    
                    img_base64 = base64.b64encode(buffer).decode()
                    return f"data:image/jpeg;base64,{img_base64}"
                    
                except Exception as e:
                    logger.error(f"Failed to convert ndarray to base64: {e}")
                    return None
        
        # Replace the original class
        video_module.VideoSurface = FixedVideoSurface
        logger.info("VideoSurface patched with video fix")
        
    except Exception as e:
        logger.error(f"Failed to patch VideoSurface: {e}")


def patch_main_app():
    """Patch main application untuk debug video display."""
    try:
        import src.presentation.__main__ as main_module
        
        # Store original method
        original_update_video = main_module.ErgonomicsApp._update_video_display
        
        def fixed_update_video_display(self, frame: np.ndarray) -> None:
            """Update video display dengan debug yang lebih baik."""
            try:
                # Check if components are valid
                if not self.video_surface or not self.is_running or not self.page:
                    logger.debug("Video update skipped: components not ready")
                    return
                
                if frame is None or frame.size == 0:
                    logger.warning("Empty frame received in video display update")
                    return
                
                success = False
                
                # Try direct update first
                try:
                    success = self.video_surface.update_frame(frame)
                    if success:
                        logger.debug("Video frame updated successfully via direct method")
                except Exception as e:
                    logger.debug(f"Direct video update failed: {e}")
                    success = False
                
                # Fallback method
                if not success:
                    try:
                        # Manual encoding and update
                        if len(frame.shape) == 3 and frame.shape[2] == 3:
                            # Resize if needed
                            target_size = (self.video_surface.width, self.video_surface.height)
                            if frame.shape[:2][::-1] != target_size:
                                frame = cv2.resize(frame, target_size, interpolation=cv2.INTER_LINEAR)
                            
                            # High quality encoding
                            encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]
                            success_encode, buffer = cv2.imencode('.jpg', frame, encode_params)
                            
                            if success_encode and buffer is not None:
                                frame_base64 = base64.b64encode(buffer).decode('utf-8')
                                data_url = f"data:image/jpeg;base64,{frame_base64}"
                                
                                if self.video_surface.image_control:
                                    self.video_surface.image_control.src_base64 = data_url
                                    success = True
                                    logger.debug("Video frame updated via fallback method")
                    except Exception as fallback_error:
                        logger.error(f"Fallback video update failed: {fallback_error}")
                
                # Update page if successful
                if success and self.page:
                    try:
                        self.page.update()
                    except Exception as e:
                        logger.debug(f"Page update failed: {e}")
                
            except Exception as e:
                logger.error(f"Video display update error: {e}")
        
        # Replace the method
        main_module.ErgonomicsApp._update_video_display = fixed_update_video_display
        logger.info("Main app video display patched")
        
    except Exception as e:
        logger.error(f"Failed to patch main app: {e}")


def demo_video_fix():
    """Launch demo dengan video feed yang diperbaiki."""
    logger.info("MEMULAI DEMO VIDEO FIX")
    logger.info("=" * 60)
    logger.info("PERBAIKAN VIDEO FEED:")
    logger.info("   - Layer stacking yang diperbaiki")
    logger.info("   - Debug video pipeline")
    logger.info("   - Fallback encoding methods")
    logger.info("   - High quality JPEG encoding")
    logger.info("   - Error handling yang lebih baik")
    logger.info("=" * 60)
    
    try:
        # Apply patches
        patch_video_surface()
        patch_main_app()
        
        # Import and run main app
        import src.presentation.__main__ as main_module
        
        logger.info("MELUNCURKAN APLIKASI DENGAN VIDEO FIX...")
        logger.info("   Video Layer: Fixed stacking order")
        logger.info("   Encoding: High quality JPEG (95%)")
        logger.info("   Debug: Enabled")
        logger.info("   Fallback: Multiple encoding methods")
        logger.info("")
        
        main_module.main()
        
    except KeyboardInterrupt:
        logger.info("Demo stopped by user")
        
    except Exception as e:
        logger.error(f"Demo error: {e}")
        logger.info("TROUBLESHOOTING:")
        logger.info("   - Check camera permissions")
        logger.info("   - Verify OpenCV installation")
        logger.info("   - Check video encoding capabilities")
        sys.exit(1)
    
    finally:
        logger.info("=" * 60)
        logger.info("VIDEO FIX DEMO COMPLETED")
        logger.info("Video feed issues should be resolved!")
        logger.info("=" * 60)


if __name__ == "__main__":
    logger.info("TESTING VIDEO FIX COMPONENTS...")
    
    # Test basic video encoding
    try:
        # Create test frame
        test_frame = np.zeros((480, 640, 3), dtype=np.uint8)
        test_frame[:, :] = [100, 150, 200]  # Blue-ish color
        
        # Test encoding
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]
        success, buffer = cv2.imencode('.jpg', test_frame, encode_params)
        
        if success:
            img_base64 = base64.b64encode(buffer).decode()
            data_url = f"data:image/jpeg;base64,{img_base64}"
            logger.info(f"Video encoding test: PASSED (length: {len(data_url)})")
        else:
            logger.error("Video encoding test: FAILED")
            
    except Exception as e:
        logger.error(f"Video encoding test error: {e}")
    
    logger.info("Starting video fix demo...")
    demo_video_fix()
