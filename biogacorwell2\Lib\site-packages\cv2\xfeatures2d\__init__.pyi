__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


BEBLID_SIZE_512_BITS: int
BEBLID_SIZE_256_BITS: int
BEBLID_BeblidSize = int
"""One of [BEBLID_SIZE_512_BITS, BEBLID_SIZE_256_BITS]"""

TEBLID_SIZE_256_BITS: int
TEBLID_SIZE_512_BITS: int
TEBLID_TeblidSize = int
"""One of [TEBLID_SIZE_256_BITS, TEBLID_SIZE_512_BITS]"""

DAISY_NRM_NONE: int
DAISY_NRM_PARTIAL: int
DAISY_NRM_FULL: int
DAISY_NRM_SIFT: int
DAISY_NormalizationType = int
"""One of [DAISY_NRM_NONE, DAISY_NRM_PARTIAL, DAISY_NRM_FULL, DAISY_NRM_SIFT]"""

PCTSignatures_L0_25: int
PCTSIGNATURES_L0_25: int
PCTSignatures_L0_5: int
PCTSIGNATURES_L0_5: int
PCTSignatures_L1: int
PCTSIGNATURES_L1: int
PCTSignatures_L2: int
PCTSIGNATURES_L2: int
PCTSignatures_L2SQUARED: int
PCTSIGNATURES_L2SQUARED: int
PCTSignatures_L5: int
PCTSIGNATURES_L5: int
PCTSignatures_L_INFINITY: int
PCTSIGNATURES_L_INFINITY: int
PCTSignatures_DistanceFunction = int
"""One of [PCTSignatures_L0_25, PCTSIGNATURES_L0_25, PCTSignatures_L0_5, PCTSIGNATURES_L0_5, PCTSignatures_L1, PCTSIGNATURES_L1, PCTSignatures_L2, PCTSIGNATURES_L2, PCTSignatures_L2SQUARED, PCTSIGNATURES_L2SQUARED, PCTSignatures_L5, PCTSIGNATURES_L5, PCTSignatures_L_INFINITY, PCTSIGNATURES_L_INFINITY]"""

PCTSignatures_UNIFORM: int
PCTSIGNATURES_UNIFORM: int
PCTSignatures_REGULAR: int
PCTSIGNATURES_REGULAR: int
PCTSignatures_NORMAL: int
PCTSIGNATURES_NORMAL: int
PCTSignatures_PointDistribution = int
"""One of [PCTSignatures_UNIFORM, PCTSIGNATURES_UNIFORM, PCTSignatures_REGULAR, PCTSIGNATURES_REGULAR, PCTSignatures_NORMAL, PCTSIGNATURES_NORMAL]"""

PCTSignatures_MINUS: int
PCTSIGNATURES_MINUS: int
PCTSignatures_GAUSSIAN: int
PCTSIGNATURES_GAUSSIAN: int
PCTSignatures_HEURISTIC: int
PCTSIGNATURES_HEURISTIC: int
PCTSignatures_SimilarityFunction = int
"""One of [PCTSignatures_MINUS, PCTSIGNATURES_MINUS, PCTSignatures_GAUSSIAN, PCTSIGNATURES_GAUSSIAN, PCTSignatures_HEURISTIC, PCTSIGNATURES_HEURISTIC]"""


# Classes
class FREAK(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, orientationNormalized: bool = ..., scaleNormalized: bool = ..., patternScale: float = ..., nOctaves: int = ..., selectedPairs: _typing.Sequence[int] = ...) -> FREAK: ...

    def setOrientationNormalized(self, orientationNormalized: bool) -> None: ...

    def getOrientationNormalized(self) -> bool: ...

    def setScaleNormalized(self, scaleNormalized: bool) -> None: ...

    def getScaleNormalized(self) -> bool: ...

    def setPatternScale(self, patternScale: float) -> None: ...

    def getPatternScale(self) -> float: ...

    def setNOctaves(self, nOctaves: int) -> None: ...

    def getNOctaves(self) -> int: ...

    def getDefaultName(self) -> str: ...


class StarDetector(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, maxSize: int = ..., responseThreshold: int = ..., lineThresholdProjected: int = ..., lineThresholdBinarized: int = ..., suppressNonmaxSize: int = ...) -> StarDetector: ...

    def setMaxSize(self, _maxSize: int) -> None: ...

    def getMaxSize(self) -> int: ...

    def setResponseThreshold(self, _responseThreshold: int) -> None: ...

    def getResponseThreshold(self) -> int: ...

    def setLineThresholdProjected(self, _lineThresholdProjected: int) -> None: ...

    def getLineThresholdProjected(self) -> int: ...

    def setLineThresholdBinarized(self, _lineThresholdBinarized: int) -> None: ...

    def getLineThresholdBinarized(self) -> int: ...

    def setSuppressNonmaxSize(self, _suppressNonmaxSize: int) -> None: ...

    def getSuppressNonmaxSize(self) -> int: ...

    def getDefaultName(self) -> str: ...


class BriefDescriptorExtractor(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, bytes: int = ..., use_orientation: bool = ...) -> BriefDescriptorExtractor: ...

    def setDescriptorSize(self, bytes: int) -> None: ...

    def getDescriptorSize(self) -> int: ...

    def setUseOrientation(self, use_orientation: bool) -> None: ...

    def getUseOrientation(self) -> bool: ...

    def getDefaultName(self) -> str: ...


class LUCID(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, lucid_kernel: int = ..., blur_kernel: int = ...) -> LUCID: ...

    def setLucidKernel(self, lucid_kernel: int) -> None: ...

    def getLucidKernel(self) -> int: ...

    def setBlurKernel(self, blur_kernel: int) -> None: ...

    def getBlurKernel(self) -> int: ...

    def getDefaultName(self) -> str: ...


class LATCH(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, bytes: int = ..., rotationInvariance: bool = ..., half_ssd_size: int = ..., sigma: float = ...) -> LATCH: ...

    def setBytes(self, bytes: int) -> None: ...

    def getBytes(self) -> int: ...

    def setRotationInvariance(self, rotationInvariance: bool) -> None: ...

    def getRotationInvariance(self) -> bool: ...

    def setHalfSSDsize(self, half_ssd_size: int) -> None: ...

    def getHalfSSDsize(self) -> int: ...

    def setSigma(self, sigma: float) -> None: ...

    def getSigma(self) -> float: ...

    def getDefaultName(self) -> str: ...


class BEBLID(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, scale_factor: float, n_bits: int = ...) -> BEBLID: ...

    def setScaleFactor(self, scale_factor: float) -> None: ...

    def getScaleFactor(self) -> float: ...

    def getDefaultName(self) -> str: ...


class TEBLID(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, scale_factor: float, n_bits: int = ...) -> TEBLID: ...

    def getDefaultName(self) -> str: ...


class DAISY(cv2.Feature2D):
    # Functions
    @classmethod
    @_typing.overload
    def create(cls, radius: float = ..., q_radius: int = ..., q_theta: int = ..., q_hist: int = ..., norm: DAISY_NormalizationType = ..., H: cv2.typing.MatLike | None = ..., interpolation: bool = ..., use_orientation: bool = ...) -> DAISY: ...
    @classmethod
    @_typing.overload
    def create(cls, radius: float = ..., q_radius: int = ..., q_theta: int = ..., q_hist: int = ..., norm: DAISY_NormalizationType = ..., H: cv2.UMat | None = ..., interpolation: bool = ..., use_orientation: bool = ...) -> DAISY: ...

    def setRadius(self, radius: float) -> None: ...

    def getRadius(self) -> float: ...

    def setQRadius(self, q_radius: int) -> None: ...

    def getQRadius(self) -> int: ...

    def setQTheta(self, q_theta: int) -> None: ...

    def getQTheta(self) -> int: ...

    def setQHist(self, q_hist: int) -> None: ...

    def getQHist(self) -> int: ...

    def setNorm(self, norm: int) -> None: ...

    def getNorm(self) -> int: ...

    @_typing.overload
    def setH(self, H: cv2.typing.MatLike) -> None: ...
    @_typing.overload
    def setH(self, H: cv2.UMat) -> None: ...

    def getH(self) -> cv2.typing.MatLike: ...

    def setInterpolation(self, interpolation: bool) -> None: ...

    def getInterpolation(self) -> bool: ...

    def setUseOrientation(self, use_orientation: bool) -> None: ...

    def getUseOrientation(self) -> bool: ...

    def getDefaultName(self) -> str: ...


class MSDDetector(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, m_patch_radius: int = ..., m_search_area_radius: int = ..., m_nms_radius: int = ..., m_nms_scale_radius: int = ..., m_th_saliency: float = ..., m_kNN: int = ..., m_scale_factor: float = ..., m_n_scales: int = ..., m_compute_orientation: bool = ...) -> MSDDetector: ...

    def setPatchRadius(self, patch_radius: int) -> None: ...

    def getPatchRadius(self) -> int: ...

    def setSearchAreaRadius(self, use_orientation: int) -> None: ...

    def getSearchAreaRadius(self) -> int: ...

    def setNmsRadius(self, nms_radius: int) -> None: ...

    def getNmsRadius(self) -> int: ...

    def setNmsScaleRadius(self, nms_scale_radius: int) -> None: ...

    def getNmsScaleRadius(self) -> int: ...

    def setThSaliency(self, th_saliency: float) -> None: ...

    def getThSaliency(self) -> float: ...

    def setKNN(self, kNN: int) -> None: ...

    def getKNN(self) -> int: ...

    def setScaleFactor(self, scale_factor: float) -> None: ...

    def getScaleFactor(self) -> float: ...

    def setNScales(self, use_orientation: int) -> None: ...

    def getNScales(self) -> int: ...

    def setComputeOrientation(self, compute_orientation: bool) -> None: ...

    def getComputeOrientation(self) -> bool: ...

    def getDefaultName(self) -> str: ...


class VGG(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, desc: int = ..., isigma: float = ..., img_normalize: bool = ..., use_scale_orientation: bool = ..., scale_factor: float = ..., dsc_normalize: bool = ...) -> VGG: ...

    def getDefaultName(self) -> str: ...

    def setSigma(self, isigma: float) -> None: ...

    def getSigma(self) -> float: ...

    def setUseNormalizeImage(self, img_normalize: bool) -> None: ...

    def getUseNormalizeImage(self) -> bool: ...

    def setUseScaleOrientation(self, use_scale_orientation: bool) -> None: ...

    def getUseScaleOrientation(self) -> bool: ...

    def setScaleFactor(self, scale_factor: float) -> None: ...

    def getScaleFactor(self) -> float: ...

    def setUseNormalizeDescriptor(self, dsc_normalize: bool) -> None: ...

    def getUseNormalizeDescriptor(self) -> bool: ...


class BoostDesc(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, desc: int = ..., use_scale_orientation: bool = ..., scale_factor: float = ...) -> BoostDesc: ...

    def getDefaultName(self) -> str: ...

    def setUseScaleOrientation(self, use_scale_orientation: bool) -> None: ...

    def getUseScaleOrientation(self) -> bool: ...

    def setScaleFactor(self, scale_factor: float) -> None: ...

    def getScaleFactor(self) -> float: ...


class PCTSignatures(cv2.Algorithm):
    # Functions
    @classmethod
    @_typing.overload
    def create(cls, initSampleCount: int = ..., initSeedCount: int = ..., pointDistribution: int = ...) -> PCTSignatures: ...
    @classmethod
    @_typing.overload
    def create(cls, initSamplingPoints: _typing.Sequence[cv2.typing.Point2f], initSeedCount: int) -> PCTSignatures: ...
    @classmethod
    @_typing.overload
    def create(cls, initSamplingPoints: _typing.Sequence[cv2.typing.Point2f], initClusterSeedIndexes: _typing.Sequence[int]) -> PCTSignatures: ...

    @_typing.overload
    def computeSignature(self, image: cv2.typing.MatLike, signature: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def computeSignature(self, image: cv2.UMat, signature: cv2.UMat | None = ...) -> cv2.UMat: ...

    def computeSignatures(self, images: _typing.Sequence[cv2.typing.MatLike], signatures: _typing.Sequence[cv2.typing.MatLike]) -> None: ...

    @staticmethod
    @_typing.overload
    def drawSignature(source: cv2.typing.MatLike, signature: cv2.typing.MatLike, result: cv2.typing.MatLike | None = ..., radiusToShorterSideRatio: float = ..., borderThickness: int = ...) -> cv2.typing.MatLike: ...
    @staticmethod
    @_typing.overload
    def drawSignature(source: cv2.UMat, signature: cv2.UMat, result: cv2.UMat | None = ..., radiusToShorterSideRatio: float = ..., borderThickness: int = ...) -> cv2.UMat: ...

    @staticmethod
    def generateInitPoints(initPoints: _typing.Sequence[cv2.typing.Point2f], count: int, pointDistribution: int) -> None: ...

    def getSampleCount(self) -> int: ...

    def getGrayscaleBits(self) -> int: ...

    def setGrayscaleBits(self, grayscaleBits: int) -> None: ...

    def getWindowRadius(self) -> int: ...

    def setWindowRadius(self, radius: int) -> None: ...

    def getWeightX(self) -> float: ...

    def setWeightX(self, weight: float) -> None: ...

    def getWeightY(self) -> float: ...

    def setWeightY(self, weight: float) -> None: ...

    def getWeightL(self) -> float: ...

    def setWeightL(self, weight: float) -> None: ...

    def getWeightA(self) -> float: ...

    def setWeightA(self, weight: float) -> None: ...

    def getWeightB(self) -> float: ...

    def setWeightB(self, weight: float) -> None: ...

    def getWeightContrast(self) -> float: ...

    def setWeightContrast(self, weight: float) -> None: ...

    def getWeightEntropy(self) -> float: ...

    def setWeightEntropy(self, weight: float) -> None: ...

    def getSamplingPoints(self) -> _typing.Sequence[cv2.typing.Point2f]: ...

    def setWeight(self, idx: int, value: float) -> None: ...

    def setWeights(self, weights: _typing.Sequence[float]) -> None: ...

    def setTranslation(self, idx: int, value: float) -> None: ...

    def setTranslations(self, translations: _typing.Sequence[float]) -> None: ...

    def setSamplingPoints(self, samplingPoints: _typing.Sequence[cv2.typing.Point2f]) -> None: ...

    def getInitSeedIndexes(self) -> _typing.Sequence[int]: ...

    def setInitSeedIndexes(self, initSeedIndexes: _typing.Sequence[int]) -> None: ...

    def getInitSeedCount(self) -> int: ...

    def getIterationCount(self) -> int: ...

    def setIterationCount(self, iterationCount: int) -> None: ...

    def getMaxClustersCount(self) -> int: ...

    def setMaxClustersCount(self, maxClustersCount: int) -> None: ...

    def getClusterMinSize(self) -> int: ...

    def setClusterMinSize(self, clusterMinSize: int) -> None: ...

    def getJoiningDistance(self) -> float: ...

    def setJoiningDistance(self, joiningDistance: float) -> None: ...

    def getDropThreshold(self) -> float: ...

    def setDropThreshold(self, dropThreshold: float) -> None: ...

    def getDistanceFunction(self) -> int: ...

    def setDistanceFunction(self, distanceFunction: int) -> None: ...


class PCTSignaturesSQFD(cv2.Algorithm):
    # Functions
    @classmethod
    def create(cls, distanceFunction: int = ..., similarityFunction: int = ..., similarityParameter: float = ...) -> PCTSignaturesSQFD: ...

    @_typing.overload
    def computeQuadraticFormDistance(self, _signature0: cv2.typing.MatLike, _signature1: cv2.typing.MatLike) -> float: ...
    @_typing.overload
    def computeQuadraticFormDistance(self, _signature0: cv2.UMat, _signature1: cv2.UMat) -> float: ...

    def computeQuadraticFormDistances(self, sourceSignature: cv2.typing.MatLike, imageSignatures: _typing.Sequence[cv2.typing.MatLike], distances: _typing.Sequence[float]) -> None: ...


class HarrisLaplaceFeatureDetector(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, numOctaves: int = ..., corn_thresh: float = ..., DOG_thresh: float = ..., maxCorners: int = ..., num_layers: int = ...) -> HarrisLaplaceFeatureDetector: ...

    def setNumOctaves(self, numOctaves_: int) -> None: ...

    def getNumOctaves(self) -> int: ...

    def setCornThresh(self, corn_thresh_: float) -> None: ...

    def getCornThresh(self) -> float: ...

    def setDOGThresh(self, DOG_thresh_: float) -> None: ...

    def getDOGThresh(self) -> float: ...

    def setMaxCorners(self, maxCorners_: int) -> None: ...

    def getMaxCorners(self) -> int: ...

    def setNumLayers(self, num_layers_: int) -> None: ...

    def getNumLayers(self) -> int: ...

    def getDefaultName(self) -> str: ...


class AffineFeature2D(cv2.Feature2D):
    ...

class TBMR(AffineFeature2D):
    # Functions
    @classmethod
    def create(cls, min_area: int = ..., max_area_relative: float = ..., scale_factor: float = ..., n_scales: int = ...) -> TBMR: ...

    def setMinArea(self, minArea: int) -> None: ...

    def getMinArea(self) -> int: ...

    def setMaxAreaRelative(self, maxArea: float) -> None: ...

    def getMaxAreaRelative(self) -> float: ...

    def setScaleFactor(self, scale_factor: float) -> None: ...

    def getScaleFactor(self) -> float: ...

    def setNScales(self, n_scales: int) -> None: ...

    def getNScales(self) -> int: ...

    def getDefaultName(self) -> str: ...


class SURF(cv2.Feature2D):
    # Functions
    @classmethod
    def create(cls, hessianThreshold: float = ..., nOctaves: int = ..., nOctaveLayers: int = ..., extended: bool = ..., upright: bool = ...) -> SURF: ...

    def setHessianThreshold(self, hessianThreshold: float) -> None: ...

    def getHessianThreshold(self) -> float: ...

    def setNOctaves(self, nOctaves: int) -> None: ...

    def getNOctaves(self) -> int: ...

    def setNOctaveLayers(self, nOctaveLayers: int) -> None: ...

    def getNOctaveLayers(self) -> int: ...

    def setExtended(self, extended: bool) -> None: ...

    def getExtended(self) -> bool: ...

    def setUpright(self, upright: bool) -> None: ...

    def getUpright(self) -> bool: ...

    def getDefaultName(self) -> str: ...



# Functions
def matchGMS(size1: cv2.typing.Size, size2: cv2.typing.Size, keypoints1: _typing.Sequence[cv2.KeyPoint], keypoints2: _typing.Sequence[cv2.KeyPoint], matches1to2: _typing.Sequence[cv2.DMatch], withRotation: bool = ..., withScale: bool = ..., thresholdFactor: float = ...) -> _typing.Sequence[cv2.DMatch]: ...

def matchLOGOS(keypoints1: _typing.Sequence[cv2.KeyPoint], keypoints2: _typing.Sequence[cv2.KeyPoint], nn1: _typing.Sequence[int], nn2: _typing.Sequence[int]) -> _typing.Sequence[cv2.DMatch]: ...


