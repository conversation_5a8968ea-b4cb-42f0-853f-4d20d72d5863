__all__: list[str] = []

import cv2
import cv2.ml
import cv2.typing
import typing as _typing


# Classes
class QualityBase(cv2.Algorithm):
    # Functions
    @_typing.overload
    def compute(self, img: cv2.typing.MatLike) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, img: cv2.UMat) -> cv2.typing.Scalar: ...

    @_typing.overload
    def getQualityMap(self, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def getQualityMap(self, dst: cv2.UMat | None = ...) -> cv2.UMat: ...

    def clear(self) -> None: ...

    def empty(self) -> bool: ...


class QualityBRISQUE(QualityBase):
    # Functions
    @_typing.overload
    def compute(self, img: cv2.typing.MatLike) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, img: cv2.UMat) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, img: cv2.typing.MatLike, model_file_path: str, range_file_path: str) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, img: cv2.UMat, model_file_path: str, range_file_path: str) -> cv2.typing.Scalar: ...

    @classmethod
    @_typing.overload
    def create(cls, model_file_path: str, range_file_path: str) -> QualityBRISQUE: ...
    @classmethod
    @_typing.overload
    def create(cls, model: cv2.ml.SVM, range: cv2.typing.MatLike) -> QualityBRISQUE: ...

    @staticmethod
    @_typing.overload
    def computeFeatures(img: cv2.typing.MatLike, features: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @staticmethod
    @_typing.overload
    def computeFeatures(img: cv2.UMat, features: cv2.UMat | None = ...) -> cv2.UMat: ...


class QualityGMSD(QualityBase):
    # Functions
    @_typing.overload
    def compute(self, cmp: cv2.typing.MatLike) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, cmp: cv2.UMat) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, ref: cv2.typing.MatLike, cmp: cv2.typing.MatLike, qualityMap: cv2.typing.MatLike | None = ...) -> tuple[cv2.typing.Scalar, cv2.typing.MatLike]: ...
    @_typing.overload
    def compute(self, ref: cv2.UMat, cmp: cv2.UMat, qualityMap: cv2.UMat | None = ...) -> tuple[cv2.typing.Scalar, cv2.UMat]: ...

    def empty(self) -> bool: ...

    def clear(self) -> None: ...

    @classmethod
    @_typing.overload
    def create(cls, ref: cv2.typing.MatLike) -> QualityGMSD: ...
    @classmethod
    @_typing.overload
    def create(cls, ref: cv2.UMat) -> QualityGMSD: ...


class QualityMSE(QualityBase):
    # Functions
    @_typing.overload
    def compute(self, cmpImgs: _typing.Sequence[cv2.typing.MatLike]) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, cmpImgs: _typing.Sequence[cv2.UMat]) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, ref: cv2.typing.MatLike, cmp: cv2.typing.MatLike, qualityMap: cv2.typing.MatLike | None = ...) -> tuple[cv2.typing.Scalar, cv2.typing.MatLike]: ...
    @_typing.overload
    def compute(self, ref: cv2.UMat, cmp: cv2.UMat, qualityMap: cv2.UMat | None = ...) -> tuple[cv2.typing.Scalar, cv2.UMat]: ...

    def empty(self) -> bool: ...

    def clear(self) -> None: ...

    @classmethod
    @_typing.overload
    def create(cls, ref: cv2.typing.MatLike) -> QualityMSE: ...
    @classmethod
    @_typing.overload
    def create(cls, ref: cv2.UMat) -> QualityMSE: ...


class QualityPSNR(QualityBase):
    # Functions
    @classmethod
    @_typing.overload
    def create(cls, ref: cv2.typing.MatLike, maxPixelValue: float = ...) -> QualityPSNR: ...
    @classmethod
    @_typing.overload
    def create(cls, ref: cv2.UMat, maxPixelValue: float = ...) -> QualityPSNR: ...

    @_typing.overload
    def compute(self, cmp: cv2.typing.MatLike) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, cmp: cv2.UMat) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, ref: cv2.typing.MatLike, cmp: cv2.typing.MatLike, qualityMap: cv2.typing.MatLike | None = ..., maxPixelValue: float = ...) -> tuple[cv2.typing.Scalar, cv2.typing.MatLike]: ...
    @_typing.overload
    def compute(self, ref: cv2.UMat, cmp: cv2.UMat, qualityMap: cv2.UMat | None = ..., maxPixelValue: float = ...) -> tuple[cv2.typing.Scalar, cv2.UMat]: ...

    def empty(self) -> bool: ...

    def clear(self) -> None: ...

    def getMaxPixelValue(self) -> float: ...

    def setMaxPixelValue(self, val: float) -> None: ...


class QualitySSIM(QualityBase):
    # Functions
    @_typing.overload
    def compute(self, cmp: cv2.typing.MatLike) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, cmp: cv2.UMat) -> cv2.typing.Scalar: ...
    @_typing.overload
    def compute(self, ref: cv2.typing.MatLike, cmp: cv2.typing.MatLike, qualityMap: cv2.typing.MatLike | None = ...) -> tuple[cv2.typing.Scalar, cv2.typing.MatLike]: ...
    @_typing.overload
    def compute(self, ref: cv2.UMat, cmp: cv2.UMat, qualityMap: cv2.UMat | None = ...) -> tuple[cv2.typing.Scalar, cv2.UMat]: ...

    def empty(self) -> bool: ...

    def clear(self) -> None: ...

    @classmethod
    @_typing.overload
    def create(cls, ref: cv2.typing.MatLike) -> QualitySSIM: ...
    @classmethod
    @_typing.overload
    def create(cls, ref: cv2.UMat) -> QualitySSIM: ...



