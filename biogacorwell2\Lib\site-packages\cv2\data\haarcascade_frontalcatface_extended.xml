<?xml version="1.0"?>
<!--
 A frontal cat face detector using the full set of Haar features, i.e.
 horizontal, vertical, and diagonal features.

 Contributed by <PERSON> (joseph<PERSON><PERSON>@nummist.com).

 More information can be found in the following publications and
 presentations:

 <PERSON>. OpenCV for Secret Agents (book). Packt Publishing, January
   2015.
 <PERSON>. "Training Detectors and Recognizers in Python and OpenCV"
   (tutorial). ISMAR 2014. September 9, 2014.
   http://nummist.com/opencv/Howse_ISMAR_20140909.pdf
 <PERSON>. "Training Intelligent Camera Systems with Python and OpenCV"
   (webcast). O’Reilly Media. June 17, 2014.
   http://www.oreilly.com/pub/e/3077

 Build scripts and demo applications can be found in the following repository:
 https://bitbucket.org/<PERSON>_<PERSON><PERSON>/angora-blue

 KNOWN LIMITATIONS:

 An upright subject is assumed. In situations where the cat's face might be
 sideways or upside down (e.g. the cat is rolling over), try various rotations
 of the input image.

 CHANGELOG:

 2016-08-06: Re-trained with more negative samples and more stages. False
   positives are much rarer now. If you tailored your code for the cascade's
   previous version, now you should re-adjust the arguments of
   CascadeClassifier::detectMultiScale. For example, decrease the value of the
   minNeighbors argument. You do not need to use a human face detector to
   cross-check the positives anymore.
 2014-04-25: First release (at https://bitbucket.org/Joe_Howse/angora-blue)

 //////////////////////////////////////////////////////////////////////////
 | Contributors License Agreement
 | IMPORTANT: READ BEFORE DOWNLOADING, COPYING, INSTALLING OR USING.
 |   By downloading, copying, installing or using the software you agree
 |   to this license.
 |   If you do not agree to this license, do not download, install,
 |   copy or use the software.
 |
 | Copyright (c) 2014-2016, Joseph Howse (Nummist Media Corporation Limited,
 | Halifax, Nova Scotia, Canada). All rights reserved.
 |
 | Redistribution and use in source and binary forms, with or without
 | modification, are permitted provided that the following conditions are
 | met:
 |
 |    * Redistributions of source code must retain the above copyright
 |       notice, this list of conditions and the following disclaimer.
 |    * Redistributions in binary form must reproduce the above
 |      copyright notice, this list of conditions and the following
 |      disclaimer in the documentation and/or other materials provided
 |      with the distribution.
 |    * The name of Contributor may not used to endorse or promote products
 |      derived from this software without specific prior written permission.
 |
 | THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS
 | "AS IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT
 | LIMITED TO, THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR
 | A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
 | CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
 | EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
 | PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
 | PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
 | LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
 | NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
 | SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.  Back to
 | Top
 //////////////////////////////////////////////////////////////////////////
 -->
<opencv_storage>
<cascade>
  <stageType>BOOST</stageType>
  <featureType>HAAR</featureType>
  <height>24</height>
  <width>24</width>
  <stageParams>
    <boostType>GAB</boostType>
    <minHitRate>9.9500000476837158e-01</minHitRate>
    <maxFalseAlarm>5.0000000000000000e-01</maxFalseAlarm>
    <weightTrimRate>9.4999999999999996e-01</weightTrimRate>
    <maxDepth>1</maxDepth>
    <maxWeakCount>100</maxWeakCount></stageParams>
  <featureParams>
    <maxCatCount>0</maxCatCount>
    <featSize>1</featSize>
    <mode>ALL</mode></featureParams>
  <stageNum>20</stageNum>
  <stages>
    <!-- stage 0 -->
    <_>
      <maxWeakCount>13</maxWeakCount>
      <stageThreshold>-1.4294912815093994e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 394 -1.5126220881938934e-02</internalNodes>
          <leafValues>
            7.5887596607208252e-01 -3.4230688214302063e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 737 3.9337221533060074e-03</internalNodes>
          <leafValues>
            -3.3288389444351196e-01 5.2361363172531128e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 757 -1.5044892206788063e-02</internalNodes>
          <leafValues>
            5.5565774440765381e-01 -2.2505992650985718e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 450 -1.5777055174112320e-02</internalNodes>
          <leafValues>
            7.2692525386810303e-01 -1.6206762194633484e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 443 3.0781796202063560e-02</internalNodes>
          <leafValues>
            -1.8173390626907349e-01 7.3483395576477051e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 220 1.8483418971300125e-02</internalNodes>
          <leafValues>
            -1.8690711259841919e-01 5.0116515159606934e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 681 1.3474167324602604e-02</internalNodes>
          <leafValues>
            -1.5681208670139313e-01 5.8611637353897095e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 5.3415738046169281e-02</internalNodes>
          <leafValues>
            -1.6418528556823730e-01 6.8128466606140137e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 741 5.4243900813162327e-03</internalNodes>
          <leafValues>
            -1.8231739103794098e-01 4.6716138720512390e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 336 1.7689792439341545e-02</internalNodes>
          <leafValues>
            -1.3713267445564270e-01 6.0434049367904663e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 2.2149257711134851e-04</internalNodes>
          <leafValues>
            -2.7738124132156372e-01 2.8165665268898010e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 288 -2.8517641127109528e-02</internalNodes>
          <leafValues>
            5.5257320404052734e-01 -1.2970162928104401e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 369 4.3854981660842896e-02</internalNodes>
          <leafValues>
            -1.9231440126895905e-01 4.2093500494956970e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 1 -->
    <_>
      <maxWeakCount>27</maxWeakCount>
      <stageThreshold>-1.5509251356124878e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 337 2.4014184251427650e-02</internalNodes>
          <leafValues>
            -2.1038578450679779e-01 7.3892170190811157e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 475 -5.5319909006357193e-03</internalNodes>
          <leafValues>
            4.4344031810760498e-01 -2.8907662630081177e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 4 2.7481060475111008e-02</internalNodes>
          <leafValues>
            -1.9128543138504028e-01 5.1661676168441772e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 -1.1628001928329468e-02</internalNodes>
          <leafValues>
            5.1978123188018799e-01 -1.7051684856414795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 1.5159824397414923e-03</internalNodes>
          <leafValues>
            -2.9784303903579712e-01 3.9050224423408508e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 901 1.3662670738995075e-02</internalNodes>
          <leafValues>
            -1.4316783845424652e-01 4.4111710786819458e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 780 -3.6911026109009981e-03</internalNodes>
          <leafValues>
            3.2185173034667969e-01 -2.3853960633277893e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 769 3.3176485449075699e-02</internalNodes>
          <leafValues>
            -7.4603199958801270e-02 7.5860917568206787e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 317 -5.7046953588724136e-03</internalNodes>
          <leafValues>
            -7.5004047155380249e-01 1.0240622609853745e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 73 7.9660946503281593e-03</internalNodes>
          <leafValues>
            9.8882928490638733e-02 -7.3491615056991577e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 739 3.0965393409132957e-02</internalNodes>
          <leafValues>
            -1.6046196222305298e-01 4.5570060610771179e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 612 -4.0078125894069672e-03</internalNodes>
          <leafValues>
            -7.1539020538330078e-01 6.9276176393032074e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 647 -8.2283765077590942e-03</internalNodes>
          <leafValues>
            3.2576236128807068e-01 -1.8509653210639954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 170 3.4253271296620369e-03</internalNodes>
          <leafValues>
            1.0964145511388779e-01 -5.8205413818359375e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 9.0980646200478077e-04</internalNodes>
          <leafValues>
            -2.0425215363502502e-01 2.7488732337951660e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 427 5.9772443026304245e-02</internalNodes>
          <leafValues>
            -1.3786207139492035e-01 4.0762668848037720e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 209 -4.1712004691362381e-02</internalNodes>
          <leafValues>
            4.9409377574920654e-01 -1.1713714897632599e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 248 -3.0311278998851776e-02</internalNodes>
          <leafValues>
            5.1191121339797974e-01 -1.0507214814424515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 339 -6.5785087645053864e-03</internalNodes>
          <leafValues>
            -7.6472043991088867e-01 8.0923363566398621e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 37 1.1685060337185860e-02</internalNodes>
          <leafValues>
            5.0379037857055664e-02 -7.9744982719421387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 6.5714016556739807e-02</internalNodes>
          <leafValues>
            -1.1398456245660782e-01 4.9489131569862366e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 755 9.7422497346997261e-03</internalNodes>
          <leafValues>
            -1.4347794651985168e-01 3.6561754345893860e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 870 4.9857441335916519e-03</internalNodes>
          <leafValues>
            7.9834438860416412e-02 -7.2391557693481445e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 -1.1547822505235672e-03</internalNodes>
          <leafValues>
            4.1867440938949585e-01 -1.2869183719158173e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 519 -4.4658007100224495e-03</internalNodes>
          <leafValues>
            -6.7933702468872070e-01 8.2867160439491272e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 862 3.6325352266430855e-03</internalNodes>
          <leafValues>
            6.6807270050048828e-02 -6.0182958841323853e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 127 7.4123376980423927e-03</internalNodes>
          <leafValues>
            -1.5108695626258850e-01 3.2046884298324585e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 2 -->
    <_>
      <maxWeakCount>26</maxWeakCount>
      <stageThreshold>-1.3890913724899292e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 619 1.7836617305874825e-02</internalNodes>
          <leafValues>
            -2.1508488059043884e-01 6.6796410083770752e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 -8.5781915113329887e-03</internalNodes>
          <leafValues>
            5.0962758064270020e-01 -2.2129471600055695e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 165 3.1586211174726486e-02</internalNodes>
          <leafValues>
            -2.1485456824302673e-01 4.2591696977615356e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 518 2.5690056383609772e-02</internalNodes>
          <leafValues>
            -1.5910078585147858e-01 6.7842948436737061e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 768 -2.2857591509819031e-02</internalNodes>
          <leafValues>
            5.7221925258636475e-01 -1.3710150122642517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 741 4.7176675871014595e-03</internalNodes>
          <leafValues>
            -2.3617559671401978e-01 3.9870622754096985e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 615 -2.3281413596123457e-03</internalNodes>
          <leafValues>
            -7.0095318555831909e-01 1.3746888935565948e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 139 1.0266102617606521e-03</internalNodes>
          <leafValues>
            -2.6873087882995605e-01 2.6495781540870667e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 2 -7.6808528974652290e-03</internalNodes>
          <leafValues>
            3.6925876140594482e-01 -2.1339643001556396e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 454 6.4357556402683258e-02</internalNodes>
          <leafValues>
            -1.1779088526964188e-01 5.5030888319015503e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 296 8.9486092329025269e-02</internalNodes>
          <leafValues>
            -1.4395782351493835e-01 5.3468054533004761e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 -5.6334878318011761e-03</internalNodes>
          <leafValues>
            -6.5704786777496338e-01 1.3971389830112457e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 -8.0200601369142532e-03</internalNodes>
          <leafValues>
            3.6956611275672913e-01 -1.8284171819686890e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 732 8.3984360098838806e-03</internalNodes>
          <leafValues>
            -1.3507588207721710e-01 4.4903004169464111e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 246 -5.7764705270528793e-03</internalNodes>
          <leafValues>
            -6.5459579229354858e-01 1.1050829291343689e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 630 3.9896301925182343e-02</internalNodes>
          <leafValues>
            -1.5822732448577881e-01 3.6069712042808533e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 -6.8376958370208740e-02</internalNodes>
          <leafValues>
            6.2642019987106323e-01 -8.3647280931472778e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 696 -2.7075063437223434e-02</internalNodes>
          <leafValues>
            4.0549215674400330e-01 -1.4247153699398041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 933 6.8107023835182190e-03</internalNodes>
          <leafValues>
            7.7754773199558258e-02 -6.4665120840072632e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 131 3.6659452598541975e-03</internalNodes>
          <leafValues>
            7.9356946051120758e-02 -5.4679936170578003e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 182 2.3308303207159042e-02</internalNodes>
          <leafValues>
            -1.4383231103420258e-01 3.4179633855819702e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 389 -3.2547116279602051e-02</internalNodes>
          <leafValues>
            3.6395668983459473e-01 -1.2551946938037872e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 471 1.6501296311616898e-02</internalNodes>
          <leafValues>
            -1.0674661397933960e-01 4.2714300751686096e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 616 -2.9296698048710823e-03</internalNodes>
          <leafValues>
            -5.7476091384887695e-01 8.5429534316062927e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 828 1.3306898763403296e-03</internalNodes>
          <leafValues>
            -1.2303277105093002e-01 3.7224721908569336e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 9.8933260887861252e-03</internalNodes>
          <leafValues>
            6.7675270140171051e-02 -6.7935848236083984e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 3 -->
    <_>
      <maxWeakCount>31</maxWeakCount>
      <stageThreshold>-1.4026626348495483e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 876 -1.4927964657545090e-02</internalNodes>
          <leafValues>
            6.3834953308105469e-01 -1.8698258697986603e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 -1.1759694665670395e-02</internalNodes>
          <leafValues>
            5.0763273239135742e-01 -2.0944127440452576e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 775 1.1289508081972599e-02</internalNodes>
          <leafValues>
            -1.4533838629722595e-01 5.3039866685867310e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 1.3691024854779243e-02</internalNodes>
          <leafValues>
            -1.3143934309482574e-01 5.9853446483612061e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 399 -8.6051290854811668e-03</internalNodes>
          <leafValues>
            3.1604155898094177e-01 -2.2497664391994476e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 898 1.1611104011535645e-02</internalNodes>
          <leafValues>
            -1.7180299758911133e-01 3.6340636014938354e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 919 5.4911419283598661e-04</internalNodes>
          <leafValues>
            -2.0625770092010498e-01 3.0243906378746033e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 448 -1.1997690424323082e-02</internalNodes>
          <leafValues>
            6.7541980743408203e-01 -1.0784135758876801e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 610 -2.0809918642044067e-03</internalNodes>
          <leafValues>
            -5.7404327392578125e-01 1.1769672483205795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 277 6.8656861782073975e-02</internalNodes>
          <leafValues>
            -1.4633083343505859e-01 4.1269731521606445e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 215 -4.5645810663700104e-02</internalNodes>
          <leafValues>
            5.4341620206832886e-01 -1.1726979166269302e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 890 -1.8052812665700912e-02</internalNodes>
          <leafValues>
            3.6646232008934021e-01 -1.3256482779979706e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 897 9.2329997569322586e-03</internalNodes>
          <leafValues>
            9.1808989644050598e-02 -6.4987671375274658e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 -2.9587259050458670e-03</internalNodes>
          <leafValues>
            2.4805040657520294e-01 -2.0830279588699341e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 151 -7.1467030793428421e-03</internalNodes>
          <leafValues>
            -6.6564339399337769e-01 8.8065519928932190e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 756 -5.7738199830055237e-03</internalNodes>
          <leafValues>
            2.4252247810363770e-01 -2.1394193172454834e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 6.4636822789907455e-03</internalNodes>
          <leafValues>
            8.4821723401546478e-02 -6.4125812053680420e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 527 -2.8782974928617477e-02</internalNodes>
          <leafValues>
            3.5874211788177490e-01 -1.4370997250080109e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 715 -1.8174832221120596e-03</internalNodes>
          <leafValues>
            3.7480926513671875e-01 -1.2761794030666351e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 590 -1.9234847277402878e-03</internalNodes>
          <leafValues>
            -5.6678783893585205e-01 9.0299606323242188e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 588 2.8048637323081493e-03</internalNodes>
          <leafValues>
            8.5870750248432159e-02 -5.8541411161422729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 178 7.0693701505661011e-02</internalNodes>
          <leafValues>
            -1.2318307906389236e-01 3.9827430248260498e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 6.2659628689289093e-02</internalNodes>
          <leafValues>
            -9.1229990124702454e-02 5.0639665126800537e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 321 -3.7420655135065317e-03</internalNodes>
          <leafValues>
            3.5059738159179688e-01 -1.2444343417882919e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 273 6.8388320505619049e-03</internalNodes>
          <leafValues>
            -1.0419095307588577e-01 4.5085826516151428e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 7.1193519979715347e-03</internalNodes>
          <leafValues>
            9.1205865144729614e-02 -5.2279585599899292e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 791 -9.8787562455981970e-04</internalNodes>
          <leafValues>
            2.8105542063713074e-01 -1.5169830620288849e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 639 1.8099821172654629e-03</internalNodes>
          <leafValues>
            6.5428622066974640e-02 -6.9196063280105591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 726 -6.0212425887584686e-03</internalNodes>
          <leafValues>
            -6.2636482715606689e-01 5.1543414592742920e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 818 5.1644006744027138e-03</internalNodes>
          <leafValues>
            6.3040286302566528e-02 -6.3455927371978760e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 205 9.4506526365876198e-03</internalNodes>
          <leafValues>
            -1.3443979620933533e-01 3.1506177783012390e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 4 -->
    <_>
      <maxWeakCount>38</maxWeakCount>
      <stageThreshold>-1.4621645212173462e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 383 -1.5925668179988861e-02</internalNodes>
          <leafValues>
            6.2127149105072021e-01 -1.8520653247833252e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 648 1.0260052047669888e-02</internalNodes>
          <leafValues>
            -2.4736632406711578e-01 4.2336893081665039e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 3 5.7025998830795288e-03</internalNodes>
          <leafValues>
            -2.3670144379138947e-01 3.3228391408920288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 264 9.3164276331663132e-03</internalNodes>
          <leafValues>
            -1.7946784198284149e-01 4.6311038732528687e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 -5.0438079051673412e-03</internalNodes>
          <leafValues>
            4.4613519310951233e-01 -1.6072992980480194e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 793 2.8381291776895523e-03</internalNodes>
          <leafValues>
            -1.8486896157264709e-01 3.5892590880393982e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 455 6.7377656698226929e-02</internalNodes>
          <leafValues>
            -1.7760114371776581e-01 3.9539518952369690e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 -8.7916189804673195e-03</internalNodes>
          <leafValues>
            -5.9182339906692505e-01 1.1145308613777161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 874 1.3353329151868820e-02</internalNodes>
          <leafValues>
            -1.1993711441755295e-01 4.8862439393997192e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 -1.0008489713072777e-02</internalNodes>
          <leafValues>
            4.1768664121627808e-01 -1.2453128397464752e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -1.4410717412829399e-03</internalNodes>
          <leafValues>
            3.4100320935249329e-01 -1.6849595308303833e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 123 1.1647527664899826e-01</internalNodes>
          <leafValues>
            -9.7596585750579834e-02 4.2289251089096069e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 301 -9.8112244158983231e-03</internalNodes>
          <leafValues>
            2.6155915856361389e-01 -2.0234876871109009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 425 6.3042029738426208e-02</internalNodes>
          <leafValues>
            -1.2662252783775330e-01 3.6811619997024536e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 553 -1.7675247043371201e-02</internalNodes>
          <leafValues>
            4.1690909862518311e-01 -1.1987055838108063e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 105 4.0485346689820290e-03</internalNodes>
          <leafValues>
            7.0249855518341064e-02 -7.3556905984878540e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 675 8.2748252898454666e-03</internalNodes>
          <leafValues>
            -1.6168670356273651e-01 2.8835350275039673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 313 -5.0843162462115288e-03</internalNodes>
          <leafValues>
            -5.8562570810317993e-01 8.9675068855285645e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 6.0826279222965240e-03</internalNodes>
          <leafValues>
            4.7766357660293579e-02 -6.8612217903137207e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 48 8.5826087743043900e-03</internalNodes>
          <leafValues>
            -1.6963686048984528e-01 2.6875671744346619e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 2.4908576160669327e-02</internalNodes>
          <leafValues>
            8.5034154355525970e-02 -5.7059210538864136e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 879 2.0448346622288227e-03</internalNodes>
          <leafValues>
            -1.8642950057983398e-01 2.3178242146968842e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 16 2.4130716919898987e-02</internalNodes>
          <leafValues>
            -1.2823060154914856e-01 3.4394741058349609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 154 -4.7494415193796158e-03</internalNodes>
          <leafValues>
            -7.1827727556228638e-01 6.8053275346755981e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 199 -1.7751917243003845e-02</internalNodes>
          <leafValues>
            -5.5972510576248169e-01 5.2141726016998291e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 339 5.5826390162110329e-03</internalNodes>
          <leafValues>
            4.8266090452671051e-02 -5.9813541173934937e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 1.4416726771742105e-03</internalNodes>
          <leafValues>
            -9.2707693576812744e-02 4.1495534777641296e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 192 -2.1779362577944994e-03</internalNodes>
          <leafValues>
            2.7112621068954468e-01 -1.5071788430213928e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 607 3.0656920280307531e-03</internalNodes>
          <leafValues>
            6.0340058058500290e-02 -6.5465551614761353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 469 1.9947460293769836e-01</internalNodes>
          <leafValues>
            -9.5098674297332764e-02 3.9016976952552795e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 857 -2.0255323499441147e-02</internalNodes>
          <leafValues>
            4.3044877052307129e-01 -8.8302992284297943e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 446 5.4685659706592560e-03</internalNodes>
          <leafValues>
            -8.7241113185882568e-02 3.9513549208641052e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 463 -1.0883151553571224e-03</internalNodes>
          <leafValues>
            2.9802373051643372e-01 -1.3696449995040894e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 655 -5.0911568105220795e-03</internalNodes>
          <leafValues>
            -6.2439930438995361e-01 6.2544539570808411e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 221 -5.2395770326256752e-03</internalNodes>
          <leafValues>
            -6.9036418199539185e-01 4.5142117887735367e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 955 4.0486194193363190e-02</internalNodes>
          <leafValues>
            -7.5753845274448395e-02 5.2426725625991821e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 300 4.1610337793827057e-03</internalNodes>
          <leafValues>
            6.6071115434169769e-02 -5.8079534769058228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 272 -6.4253048039972782e-03</internalNodes>
          <leafValues>
            3.0481830239295959e-01 -1.1435022950172424e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 5 -->
    <_>
      <maxWeakCount>44</maxWeakCount>
      <stageThreshold>-1.4235107898712158e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 716 -2.2738082334399223e-03</internalNodes>
          <leafValues>
            5.9519726037979126e-01 -1.6779936850070953e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 457 -1.2204157188534737e-02</internalNodes>
          <leafValues>
            4.6985983848571777e-01 -1.7339397966861725e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 754 3.1242824625223875e-03</internalNodes>
          <leafValues>
            -2.2488421201705933e-01 3.4029743075370789e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 777 -3.9868438616394997e-03</internalNodes>
          <leafValues>
            3.8314539194107056e-01 -1.8952924013137817e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 538 -5.4737669415771961e-03</internalNodes>
          <leafValues>
            2.4583901464939117e-01 -2.3114782571792603e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 453 1.5154287219047546e-02</internalNodes>
          <leafValues>
            -1.0675037652254105e-01 5.8347207307815552e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 -1.4294658321887255e-03</internalNodes>
          <leafValues>
            3.8292840123176575e-01 -1.2911921739578247e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 750 -7.4405185878276825e-03</internalNodes>
          <leafValues>
            2.8356546163558960e-01 -1.7810684442520142e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 786 -4.0357224643230438e-03</internalNodes>
          <leafValues>
            2.6303085684776306e-01 -1.6862161457538605e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 618 -5.8342106640338898e-03</internalNodes>
          <leafValues>
            3.2040205597877502e-01 -1.4103877544403076e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 161 1.7279960215091705e-02</internalNodes>
          <leafValues>
            -1.7433850467205048e-01 2.7985212206840515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 292 2.2125110030174255e-02</internalNodes>
          <leafValues>
            -1.1797516793012619e-01 4.0373948216438293e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 958 -4.4059187173843384e-02</internalNodes>
          <leafValues>
            5.2820503711700439e-01 -7.0916719734668732e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 -3.8316637277603149e-02</internalNodes>
          <leafValues>
            3.8833045959472656e-01 -1.0811555385589600e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 178 4.5704744756221771e-02</internalNodes>
          <leafValues>
            -1.7566929757595062e-01 3.4665411710739136e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 1.1523386929184198e-03</internalNodes>
          <leafValues>
            -1.7257389426231384e-01 2.5989890098571777e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 -1.0491746477782726e-02</internalNodes>
          <leafValues>
            -6.1285555362701416e-01 7.1230083703994751e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 -4.5014433562755585e-03</internalNodes>
          <leafValues>
            -5.7712453603744507e-01 5.8887075632810593e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 950 -3.7281280383467674e-03</internalNodes>
          <leafValues>
            -6.7359894514083862e-01 5.2957162261009216e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 331 3.4461893141269684e-02</internalNodes>
          <leafValues>
            -1.0375578701496124e-01 3.7974634766578674e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 462 -1.3906960375607014e-03</internalNodes>
          <leafValues>
            3.9171192049980164e-01 -1.0048408061265945e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 1.6332454979419708e-02</internalNodes>
          <leafValues>
            8.6256101727485657e-02 -4.5887523889541626e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 356 -6.0738036409020424e-03</internalNodes>
          <leafValues>
            -5.2265202999114990e-01 6.5308839082717896e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 486 -3.3630726393312216e-03</internalNodes>
          <leafValues>
            -5.6505429744720459e-01 5.5844355374574661e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 418 -1.5329496003687382e-02</internalNodes>
          <leafValues>
            3.4475114941596985e-01 -1.0086353123188019e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 587 -9.0496204793453217e-03</internalNodes>
          <leafValues>
            2.9553902149200439e-01 -1.1406829208135605e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 794 -3.1109917908906937e-03</internalNodes>
          <leafValues>
            -4.4897687435150146e-01 7.3615357279777527e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 939 3.3499556593596935e-03</internalNodes>
          <leafValues>
            5.4718658328056335e-02 -5.4810231924057007e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 1.8374501960352063e-03</internalNodes>
          <leafValues>
            -1.3522666692733765e-01 2.4655479192733765e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 908 2.6134990621358156e-03</internalNodes>
          <leafValues>
            6.6369861364364624e-02 -4.7342041134834290e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 65 -7.4155852198600769e-03</internalNodes>
          <leafValues>
            2.0866124331951141e-01 -1.5775154531002045e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 515 3.9352793246507645e-03</internalNodes>
          <leafValues>
            5.1660846918821335e-02 -6.2589824199676514e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 -1.0450070258229971e-03</internalNodes>
          <leafValues>
            3.3525371551513672e-01 -1.0084854811429977e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 784 1.2639444321393967e-03</internalNodes>
          <leafValues>
            -1.2103077769279480e-01 2.7691018581390381e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 479 7.7577251940965652e-03</internalNodes>
          <leafValues>
            4.6813234686851501e-02 -7.3385792970657349e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 18 -1.0632604360580444e-02</internalNodes>
          <leafValues>
            -7.1024382114410400e-01 3.3777639269828796e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 183 1.8631946295499802e-02</internalNodes>
          <leafValues>
            -1.4613701403141022e-01 2.1491082012653351e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 608 4.9128942191600800e-03</internalNodes>
          <leafValues>
            5.3445268422365189e-02 -6.3314527273178101e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 473 -9.8230186849832535e-03</internalNodes>
          <leafValues>
            2.6917773485183716e-01 -1.1376978456974030e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 910 -3.0754944309592247e-03</internalNodes>
          <leafValues>
            -5.0787961483001709e-01 6.1582125723361969e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 659 -6.7374799400568008e-03</internalNodes>
          <leafValues>
            2.3871047794818878e-01 -1.2552142143249512e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 507 -1.1759715154767036e-02</internalNodes>
          <leafValues>
            3.3646693825721741e-01 -9.4460532069206238e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 318 -4.1377237066626549e-03</internalNodes>
          <leafValues>
            -5.0522220134735107e-01 6.2668189406394958e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 320 1.7267453949898481e-03</internalNodes>
          <leafValues>
            -8.0607026815414429e-02 3.8304185867309570e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 6 -->
    <_>
      <maxWeakCount>47</maxWeakCount>
      <stageThreshold>-1.4313566684722900e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 882 -1.1920252814888954e-02</internalNodes>
          <leafValues>
            5.6617152690887451e-01 -1.5811842679977417e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 568 -4.3085627257823944e-03</internalNodes>
          <leafValues>
            4.4759327173233032e-01 -1.6846470534801483e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 883 1.1177745182067156e-03</internalNodes>
          <leafValues>
            -1.5351393818855286e-01 4.3508940935134888e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 798 3.5418532788753510e-02</internalNodes>
          <leafValues>
            -1.2973460555076599e-01 3.6943939328193665e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 393 2.2405586205422878e-03</internalNodes>
          <leafValues>
            -1.8800468742847443e-01 3.2498928904533386e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 -1.7982896417379379e-02</internalNodes>
          <leafValues>
            4.5607218146324158e-01 -1.0459473729133606e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 152 -4.9088716506958008e-02</internalNodes>
          <leafValues>
            3.4279289841651917e-01 -1.5114119648933411e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 275 7.1780886501073837e-03</internalNodes>
          <leafValues>
            6.3825756311416626e-02 -6.2449872493743896e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 849 3.9123920723795891e-03</internalNodes>
          <leafValues>
            7.1502417325973511e-02 -6.3956946134567261e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 689 -4.1980943642556667e-03</internalNodes>
          <leafValues>
            2.1998657286167145e-01 -1.9890366494655609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 660 -4.5476644299924374e-03</internalNodes>
          <leafValues>
            2.1866278350353241e-01 -1.9852560758590698e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 944 -4.4158436357975006e-03</internalNodes>
          <leafValues>
            2.3959043622016907e-01 -1.7090958356857300e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 -4.7058244235813618e-03</internalNodes>
          <leafValues>
            -5.1537507772445679e-01 9.0310461819171906e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 116 -8.7488889694213867e-03</internalNodes>
          <leafValues>
            2.2937677800655365e-01 -1.8315380811691284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 645 -3.1655649654567242e-03</internalNodes>
          <leafValues>
            -7.3091191053390503e-01 6.5193220973014832e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 267 6.4696683548390865e-03</internalNodes>
          <leafValues>
            -1.1077737808227539e-01 3.7207809090614319e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 615 2.2985613904893398e-03</internalNodes>
          <leafValues>
            7.7800542116165161e-02 -5.1104581356048584e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 359 4.5809363946318626e-03</internalNodes>
          <leafValues>
            5.7778771966695786e-02 -5.7898092269897461e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 188 1.1279166210442781e-03</internalNodes>
          <leafValues>
            -1.7981146275997162e-01 1.9939005374908447e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 -1.2820301577448845e-02</internalNodes>
          <leafValues>
            5.1867282390594482e-01 -6.9989629089832306e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 810 4.4866472482681274e-02</internalNodes>
          <leafValues>
            -1.4253044128417969e-01 3.0062338709831238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 412 -3.5413210280239582e-03</internalNodes>
          <leafValues>
            -5.7618641853332520e-01 6.0328345745801926e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 362 -7.4678594246506691e-03</internalNodes>
          <leafValues>
            -5.0187259912490845e-01 6.1294022947549820e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 678 1.8058011308312416e-02</internalNodes>
          <leafValues>
            5.3603217005729675e-02 -5.8919399976730347e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 935 -6.8098572082817554e-03</internalNodes>
          <leafValues>
            -5.4100829362869263e-01 5.5898215621709824e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 307 3.6491458304226398e-03</internalNodes>
          <leafValues>
            4.7378763556480408e-02 -5.9323132038116455e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 1.4524955768138170e-03</internalNodes>
          <leafValues>
            -8.8994570076465607e-02 3.8729071617126465e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 219 -6.2408884987235069e-03</internalNodes>
          <leafValues>
            -6.6442847251892090e-01 5.1082015037536621e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 744 -9.9360430613160133e-04</internalNodes>
          <leafValues>
            3.2972389459609985e-01 -1.0494423657655716e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 285 3.9777760393917561e-03</internalNodes>
          <leafValues>
            5.4083213210105896e-02 -6.2114214897155762e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 380 -1.4884659089148045e-02</internalNodes>
          <leafValues>
            2.4066454172134399e-01 -1.2317410856485367e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 436 3.3154981210827827e-03</internalNodes>
          <leafValues>
            -1.1744727939367294e-01 2.9429042339324951e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 976 -4.7508114948868752e-03</internalNodes>
          <leafValues>
            -4.5763325691223145e-01 6.7066885530948639e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 779 -1.1973761022090912e-02</internalNodes>
          <leafValues>
            2.5750914216041565e-01 -1.1354148387908936e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 740 4.9072699621319771e-03</internalNodes>
          <leafValues>
            -1.1266437917947769e-01 3.0022394657135010e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 56 6.5630510449409485e-02</internalNodes>
          <leafValues>
            -1.0180503129959106e-01 3.0517497658729553e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 354 -2.3393325507640839e-02</internalNodes>
          <leafValues>
            3.2443770766258240e-01 -9.5363102853298187e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 -3.8902116939425468e-03</internalNodes>
          <leafValues>
            2.0148487389087677e-01 -1.4944279193878174e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 185 -2.5926973670721054e-02</internalNodes>
          <leafValues>
            -4.4917497038841248e-01 6.9752328097820282e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 173 -7.1825529448688030e-03</internalNodes>
          <leafValues>
            -5.6838059425354004e-01 4.9584377557039261e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 548 -9.9399685859680176e-03</internalNodes>
          <leafValues>
            3.0747908353805542e-01 -1.1064232140779495e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 978 -3.6286246031522751e-03</internalNodes>
          <leafValues>
            -6.0276371240615845e-01 5.2405584603548050e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 820 1.5756220091134310e-03</internalNodes>
          <leafValues>
            -1.1615782976150513e-01 2.6717522740364075e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 426 3.5662509500980377e-02</internalNodes>
          <leafValues>
            -1.0885569453239441e-01 2.9044550657272339e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 5.3282946348190308e-02</internalNodes>
          <leafValues>
            -8.1855505704879761e-02 4.0298762917518616e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 988 3.3901704009622335e-03</internalNodes>
          <leafValues>
            5.5047694593667984e-02 -5.4021596908569336e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 384 1.3204356655478477e-03</internalNodes>
          <leafValues>
            -9.4643965363502502e-02 3.0430349707603455e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 7 -->
    <_>
      <maxWeakCount>48</maxWeakCount>
      <stageThreshold>-1.3744181394577026e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 788 3.9594387635588646e-03</internalNodes>
          <leafValues>
            -1.5454453229904175e-01 4.9922767281532288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 -1.6322813928127289e-02</internalNodes>
          <leafValues>
            4.2537182569503784e-01 -1.5276345610618591e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 746 1.6230947803705931e-03</internalNodes>
          <leafValues>
            -2.2640861570835114e-01 2.5220483541488647e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 115 -6.0441931709647179e-03</internalNodes>
          <leafValues>
            2.2711095213890076e-01 -2.1762822568416595e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 6 1.1688062921166420e-02</internalNodes>
          <leafValues>
            -1.6991630196571350e-01 2.8343129158020020e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 624 -3.1942571513354778e-03</internalNodes>
          <leafValues>
            -6.2475329637527466e-01 7.3184341192245483e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 -7.6569117605686188e-02</internalNodes>
          <leafValues>
            5.5236744880676270e-01 -7.7832877635955811e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 306 1.8717286875471473e-03</internalNodes>
          <leafValues>
            8.4293909370899200e-02 -5.2716743946075439e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 351 3.5880310460925102e-03</internalNodes>
          <leafValues>
            -1.2907223403453827e-01 3.3967444300651550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 176 -5.7136151008307934e-03</internalNodes>
          <leafValues>
            -5.9208476543426514e-01 7.7793844044208527e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 150 -1.9309867173433304e-02</internalNodes>
          <leafValues>
            2.5386241078376770e-01 -1.7397734522819519e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 327 -2.4289516732096672e-03</internalNodes>
          <leafValues>
            3.2221227884292603e-01 -1.2751287221908569e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 25 -8.5500031709671021e-02</internalNodes>
          <leafValues>
            -7.7962499856948853e-01 5.0715133547782898e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 770 5.7447291910648346e-03</internalNodes>
          <leafValues>
            -1.1523491144180298e-01 3.6400210857391357e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 781 5.8936916291713715e-02</internalNodes>
          <leafValues>
            -8.7829843163490295e-02 4.1893997788429260e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 984 -4.1379006579518318e-03</internalNodes>
          <leafValues>
            -6.3083720207214355e-01 6.4935714006423950e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 565 -4.6407114714384079e-03</internalNodes>
          <leafValues>
            -6.5650087594985962e-01 5.4394256323575974e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 877 1.5865347813814878e-03</internalNodes>
          <leafValues>
            -1.7255148291587830e-01 2.3248092830181122e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 624 2.8971401043236256e-03</internalNodes>
          <leafValues>
            6.0526229441165924e-02 -5.4368048906326294e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 773 1.5737174544483423e-03</internalNodes>
          <leafValues>
            -1.1744406074285507e-01 3.0534917116165161e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 609 1.6838097944855690e-03</internalNodes>
          <leafValues>
            6.6153712570667267e-02 -5.9224641323089600e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 912 3.2287575304508209e-03</internalNodes>
          <leafValues>
            5.2678912878036499e-02 -5.7474386692047119e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 850 -3.1512752175331116e-03</internalNodes>
          <leafValues>
            3.7773844599723816e-01 -8.7322145700454712e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 8.2073279190808535e-04</internalNodes>
          <leafValues>
            -1.0513201355934143e-01 3.4025487303733826e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 603 2.8983387164771557e-03</internalNodes>
          <leafValues>
            5.1720291376113892e-02 -6.5431916713714600e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 852 -5.7246205396950245e-03</internalNodes>
          <leafValues>
            -7.8483843803405762e-01 3.5195719450712204e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 44 -1.1572695337235928e-02</internalNodes>
          <leafValues>
            -6.7286187410354614e-01 3.5210411995649338e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 80 -1.4562263153493404e-02</internalNodes>
          <leafValues>
            2.4655815958976746e-01 -1.2278749793767929e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 269 7.8490225132554770e-04</internalNodes>
          <leafValues>
            -1.4652141928672791e-01 3.0276218056678772e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 725 -1.4289810787886381e-03</internalNodes>
          <leafValues>
            1.8906314671039581e-01 -1.5791040658950806e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 108 -9.4615388661623001e-03</internalNodes>
          <leafValues>
            -6.9036215543746948e-01 3.9911076426506042e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 21 2.3225568234920502e-02</internalNodes>
          <leafValues>
            5.0278317183256149e-02 -5.2323836088180542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 959 1.4046948403120041e-02</internalNodes>
          <leafValues>
            -7.9005211591720581e-02 4.0158179402351379e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 126 3.7851710803806782e-03</internalNodes>
          <leafValues>
            -1.3530673086643219e-01 2.1973098814487457e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 142 -3.6725951358675957e-03</internalNodes>
          <leafValues>
            1.9924460351467133e-01 -1.5001934766769409e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 963 -3.1669549643993378e-03</internalNodes>
          <leafValues>
            -4.2041611671447754e-01 7.4019186198711395e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 695 -1.3667810708284378e-02</internalNodes>
          <leafValues>
            2.5204744935035706e-01 -1.2807497382164001e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 214 -3.5862527787685394e-02</internalNodes>
          <leafValues>
            3.2997950911521912e-01 -8.9863941073417664e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 946 -6.2667285092175007e-03</internalNodes>
          <leafValues>
            -5.5024039745330811e-01 5.7369034737348557e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 438 -6.4383493736386299e-03</internalNodes>
          <leafValues>
            3.3817592263221741e-01 -9.3247875571250916e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 439 5.4173925891518593e-03</internalNodes>
          <leafValues>
            -1.0427469760179520e-01 2.9482829570770264e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 400 -1.5132453292608261e-02</internalNodes>
          <leafValues>
            3.2000914216041565e-01 -9.8272062838077545e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 606 -1.2513613328337669e-02</internalNodes>
          <leafValues>
            2.8962445259094238e-01 -1.2084391713142395e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 91 -9.8966564983129501e-03</internalNodes>
          <leafValues>
            -5.8358079195022583e-01 5.1291342824697495e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 932 1.3835988938808441e-02</internalNodes>
          <leafValues>
            -9.0702146291732788e-02 3.2527267932891846e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 92 3.6492943763732910e-03</internalNodes>
          <leafValues>
            8.4720104932785034e-02 -3.4649613499641418e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 478 -1.3878188095986843e-02</internalNodes>
          <leafValues>
            2.9309025406837463e-01 -9.6585884690284729e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 580 2.8816664125770330e-03</internalNodes>
          <leafValues>
            -1.0839603841304779e-01 2.5134062767028809e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 8 -->
    <_>
      <maxWeakCount>57</maxWeakCount>
      <stageThreshold>-1.3757541179656982e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 742 -4.1507836431264877e-03</internalNodes>
          <leafValues>
            4.7857573628425598e-01 -1.5079282224178314e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 539 -4.2431484907865524e-03</internalNodes>
          <leafValues>
            2.7976706624031067e-01 -2.1182695031166077e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 422 7.2727665305137634e-02</internalNodes>
          <leafValues>
            -1.1322361230850220e-01 4.6931907534599304e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 120 7.3349894955754280e-03</internalNodes>
          <leafValues>
            -2.2507375478744507e-01 2.3486614227294922e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 79 -1.3757663965225220e-01</internalNodes>
          <leafValues>
            5.5153369903564453e-01 -8.4895148873329163e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 592 6.8098353222012520e-04</internalNodes>
          <leafValues>
            -1.7585472762584686e-01 2.2849111258983612e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 110 2.7579340338706970e-01</internalNodes>
          <leafValues>
            -1.1671220511198044e-01 3.2674804329872131e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 921 5.4910051403567195e-04</internalNodes>
          <leafValues>
            -2.0603717863559723e-01 1.8896938860416412e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 155 -5.5065844208002090e-03</internalNodes>
          <leafValues>
            -5.7701790332794189e-01 6.9212622940540314e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 824 -8.3996364846825600e-03</internalNodes>
          <leafValues>
            4.6683028340339661e-01 -7.4202880263328552e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 843 -1.1010931339114904e-03</internalNodes>
          <leafValues>
            1.9711431860923767e-01 -1.7736457288265228e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 217 -4.4837296009063721e-03</internalNodes>
          <leafValues>
            -6.0108631849288940e-01 4.9327563494443893e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 2.5086081586778164e-03</internalNodes>
          <leafValues>
            6.9480538368225098e-02 -4.8671180009841919e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 1.5808893367648125e-03</internalNodes>
          <leafValues>
            -1.0519328713417053e-01 3.2050549983978271e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 1.4971228083595634e-03</internalNodes>
          <leafValues>
            -8.4364958107471466e-02 4.3016371130943298e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 343 -2.6089220773428679e-03</internalNodes>
          <leafValues>
            -4.2146065831184387e-01 8.8990658521652222e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 42 -7.7147269621491432e-03</internalNodes>
          <leafValues>
            -6.6330111026763916e-01 5.0671890377998352e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 85 -1.7141735181212425e-02</internalNodes>
          <leafValues>
            -4.8750495910644531e-01 5.6981299072504044e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 146 1.3850606046617031e-02</internalNodes>
          <leafValues>
            7.4964463710784912e-02 -4.4079580903053284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 -1.4932476915419102e-03</internalNodes>
          <leafValues>
            3.1057041883468628e-01 -1.0369800031185150e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 382 -8.3094676956534386e-03</internalNodes>
          <leafValues>
            2.2514784336090088e-01 -1.4621259272098541e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 462 -7.2969077154994011e-04</internalNodes>
          <leafValues>
            2.6934301853179932e-01 -1.2512375414371490e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 430 -1.3652374967932701e-02</internalNodes>
          <leafValues>
            -4.9215099215507507e-01 7.3141731321811676e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 20 9.4011947512626648e-03</internalNodes>
          <leafValues>
            4.1364993900060654e-02 -6.5001028776168823e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 657 4.0921592153608799e-03</internalNodes>
          <leafValues>
            4.0478449314832687e-02 -5.9830683469772339e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 1.5591707779094577e-03</internalNodes>
          <leafValues>
            -9.3049824237823486e-02 3.1007137894630432e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 973 3.4408085048198700e-03</internalNodes>
          <leafValues>
            4.7337688505649567e-02 -6.5880972146987915e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 -1.3411687687039375e-03</internalNodes>
          <leafValues>
            2.8307750821113586e-01 -1.0693576931953430e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 534 -5.7181939482688904e-03</internalNodes>
          <leafValues>
            -4.7754487395286560e-01 6.3519261777400970e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 374 -5.0096530467271805e-03</internalNodes>
          <leafValues>
            -6.1091655492782593e-01 3.9555240422487259e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 -4.1508115828037262e-03</internalNodes>
          <leafValues>
            2.1694649755954742e-01 -1.3193054497241974e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 844 -1.6968715935945511e-02</internalNodes>
          <leafValues>
            2.7644789218902588e-01 -1.0202119499444962e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 103 1.0276203043758869e-02</internalNodes>
          <leafValues>
            -9.0598084032535553e-02 2.9703584313392639e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 350 -1.8649294506758451e-03</internalNodes>
          <leafValues>
            2.8791305422782898e-01 -9.2735975980758667e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 942 3.3354205079376698e-03</internalNodes>
          <leafValues>
            5.3746312856674194e-02 -5.0940161943435669e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 396 -1.4105688314884901e-03</internalNodes>
          <leafValues>
            2.4489782750606537e-01 -1.1008579283952713e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 611 2.3928448557853699e-02</internalNodes>
          <leafValues>
            5.2839644253253937e-02 -4.9896511435508728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 807 -3.8580424152314663e-03</internalNodes>
          <leafValues>
            -4.8197838664054871e-01 5.3767576813697815e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 679 -3.0590491369366646e-03</internalNodes>
          <leafValues>
            -5.2978992462158203e-01 4.6741079539060593e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 468 -2.9391471762210131e-03</internalNodes>
          <leafValues>
            -3.4711557626724243e-01 6.9464050233364105e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 667 -7.0184348151087761e-03</internalNodes>
          <leafValues>
            3.1962895393371582e-01 -8.3362981677055359e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 664 1.0384586639702320e-03</internalNodes>
          <leafValues>
            -1.0797444730997086e-01 2.4896475672721863e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 628 -8.0418614670634270e-03</internalNodes>
          <leafValues>
            -7.3527222871780396e-01 3.6740459501743317e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 193 -3.1738542020320892e-02</internalNodes>
          <leafValues>
            2.6166516542434692e-01 -1.0992183536291122e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 194 3.6780342459678650e-02</internalNodes>
          <leafValues>
            -8.7741106748580933e-02 3.7106978893280029e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 494 -6.4193591475486755e-02</internalNodes>
          <leafValues>
            3.1807181239128113e-01 -8.8648937642574310e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 46 3.4801474213600159e-01</internalNodes>
          <leafValues>
            -5.5967021733522415e-02 5.3631168603897095e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 490 7.5712919235229492e-02</internalNodes>
          <leafValues>
            -5.9786085039377213e-02 4.1973164677619934e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 983 7.8374873846769333e-03</internalNodes>
          <leafValues>
            -6.8252839148044586e-02 3.9001336693763733e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 867 3.3967243507504463e-03</internalNodes>
          <leafValues>
            5.7270396500825882e-02 -4.7492286562919617e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 158 3.2095968723297119e-02</internalNodes>
          <leafValues>
            3.0982470139861107e-02 -7.2973543405532837e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 939 4.1734268888831139e-03</internalNodes>
          <leafValues>
            3.0397623777389526e-02 -6.8009066581726074e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 545 3.2336891163140535e-03</internalNodes>
          <leafValues>
            -9.4194613397121429e-02 2.5351443886756897e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 55 -3.8070861250162125e-02</internalNodes>
          <leafValues>
            2.7447724342346191e-01 -8.3862110972404480e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 358 4.6657784841954708e-03</internalNodes>
          <leafValues>
            3.7179920822381973e-02 -6.7654901742935181e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 247 -3.9379103109240532e-03</internalNodes>
          <leafValues>
            -5.9923279285430908e-01 3.2963614910840988e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 699 -4.8031057231128216e-03</internalNodes>
          <leafValues>
            2.2248022258281708e-01 -1.0560184717178345e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 9 -->
    <_>
      <maxWeakCount>55</maxWeakCount>
      <stageThreshold>-1.3843152523040771e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 456 6.7532630637288094e-03</internalNodes>
          <leafValues>
            -1.5934121608734131e-01 5.1630091667175293e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 685 1.6582473181188107e-03</internalNodes>
          <leafValues>
            -1.4192129671573639e-01 4.6970281004905701e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 741 8.5381623357534409e-03</internalNodes>
          <leafValues>
            -1.4064009487628937e-01 4.3454051017761230e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 711 -5.8347072452306747e-02</internalNodes>
          <leafValues>
            4.8053690791130066e-01 -1.1435888707637787e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 200 7.5503322295844555e-04</internalNodes>
          <leafValues>
            -1.6613751649856567e-01 3.5059270262718201e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 463 -1.6263198340311646e-03</internalNodes>
          <leafValues>
            3.3983412384986877e-01 -1.2952369451522827e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 982 -4.9476943910121918e-02</internalNodes>
          <leafValues>
            5.1085108518600464e-01 -7.6757252216339111e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 1.5736839268356562e-03</internalNodes>
          <leafValues>
            -9.8503805696964264e-02 4.2097148299217224e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 970 2.8940830379724503e-03</internalNodes>
          <leafValues>
            8.0476768314838409e-02 -5.9272909164428711e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 470 -8.5198890883475542e-04</internalNodes>
          <leafValues>
            2.7713751792907715e-01 -1.2991340458393097e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 513 -3.2718123402446508e-03</internalNodes>
          <leafValues>
            3.1215441226959229e-01 -1.2980756163597107e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 244 6.0219354927539825e-03</internalNodes>
          <leafValues>
            7.2135269641876221e-02 -5.9813290834426880e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 81 2.3065296933054924e-02</internalNodes>
          <leafValues>
            7.1330830454826355e-02 -5.3722465038299561e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 187 2.7176631192560308e-05</internalNodes>
          <leafValues>
            -2.6853099465370178e-01 1.4315985143184662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 5.4575498215854168e-03</internalNodes>
          <leafValues>
            5.5034745484590530e-02 -5.7176333665847778e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 2.5911496777553111e-05</internalNodes>
          <leafValues>
            -2.3133303225040436e-01 1.4060766994953156e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 2.1752633154392242e-02</internalNodes>
          <leafValues>
            5.9929180890321732e-02 -5.0224888324737549e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 860 3.5099866800010204e-03</internalNodes>
          <leafValues>
            4.7387380152940750e-02 -5.8126205205917358e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 755 8.6558861657977104e-03</internalNodes>
          <leafValues>
            -1.3651072978973389e-01 2.2407715022563934e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 990 3.0432851053774357e-03</internalNodes>
          <leafValues>
            5.7905938476324081e-02 -5.5585581064224243e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 240 3.4083288628607988e-03</internalNodes>
          <leafValues>
            4.6358574181795120e-02 -5.6204903125762939e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 241 -4.1327420622110367e-03</internalNodes>
          <leafValues>
            -4.3748503923416138e-01 6.6312022507190704e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 887 5.4382300004363060e-04</internalNodes>
          <leafValues>
            -1.2188895046710968e-01 2.6694831252098083e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 2.0359107293188572e-03</internalNodes>
          <leafValues>
            -6.9375663995742798e-02 4.1734528541564941e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 5.6087510893121362e-04</internalNodes>
          <leafValues>
            -1.2235503643751144e-01 2.9018589854240417e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 957 5.4084453731775284e-03</internalNodes>
          <leafValues>
            5.1494579762220383e-02 -6.3784217834472656e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 99 1.9748538732528687e-02</internalNodes>
          <leafValues>
            -7.0414997637271881e-02 4.8995351791381836e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 147 -2.0231239497661591e-02</internalNodes>
          <leafValues>
            -5.9452813863754272e-01 5.5317912250757217e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 763 -8.5184378549456596e-03</internalNodes>
          <leafValues>
            -4.9081006646156311e-01 5.1023125648498535e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 952 6.4936149865388870e-03</internalNodes>
          <leafValues>
            -8.6577519774436951e-02 3.6036944389343262e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 30 -4.0995404124259949e-02</internalNodes>
          <leafValues>
            4.0132537484169006e-01 -7.1912504732608795e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 501 3.1340471468865871e-03</internalNodes>
          <leafValues>
            -1.2547470629215240e-01 2.2158138453960419e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 184 -1.9882351160049438e-02</internalNodes>
          <leafValues>
            -7.1213179826736450e-01 4.2412471026182175e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 559 2.0461969077587128e-02</internalNodes>
          <leafValues>
            -1.0324169695377350e-01 2.9102885723114014e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 686 -1.2761610560119152e-03</internalNodes>
          <leafValues>
            2.3810100555419922e-01 -1.1509060114622116e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 549 -3.3783772960305214e-03</internalNodes>
          <leafValues>
            -5.6838840246200562e-01 5.6331343948841095e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 302 5.0912564620375633e-03</internalNodes>
          <leafValues>
            4.7987211495637894e-02 -4.7997272014617920e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 508 -4.1752815246582031e-02</internalNodes>
          <leafValues>
            -5.9290748834609985e-01 4.2219188064336777e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 263 -1.3672109693288803e-02</internalNodes>
          <leafValues>
            2.7416154742240906e-01 -9.8633147776126862e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 329 4.5463615097105503e-03</internalNodes>
          <leafValues>
            -9.5323033630847931e-02 3.3586710691452026e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 472 -1.1957241222262383e-02</internalNodes>
          <leafValues>
            1.6140049695968628e-01 -1.6837921738624573e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 95 -2.4866103194653988e-03</internalNodes>
          <leafValues>
            -3.8348227739334106e-01 6.6880211234092712e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 130 3.3222150523215532e-03</internalNodes>
          <leafValues>
            4.9669362604618073e-02 -5.2419567108154297e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 767 1.2700627557933331e-03</internalNodes>
          <leafValues>
            -1.0981336981058121e-01 2.4314954876899719e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 643 -4.0526064112782478e-03</internalNodes>
          <leafValues>
            -5.4617625474929810e-01 4.6236973255872726e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 889 -1.7611857037991285e-03</internalNodes>
          <leafValues>
            2.0527404546737671e-01 -1.1924317479133606e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 832 -2.8845192864537239e-03</internalNodes>
          <leafValues>
            2.0061042904853821e-01 -1.4499643445014954e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 969 -9.4242449849843979e-03</internalNodes>
          <leafValues>
            -7.2513866424560547e-01 3.4894362092018127e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 972 3.7029895465821028e-03</internalNodes>
          <leafValues>
            5.5003125220537186e-02 -4.1173446178436279e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 785 -8.4825151134282351e-04</internalNodes>
          <leafValues>
            2.6719486713409424e-01 -9.9083028733730316e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 54 1.5727356076240540e-02</internalNodes>
          <leafValues>
            -1.2551975250244141e-01 2.0588764548301697e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 106 5.9068910777568817e-03</internalNodes>
          <leafValues>
            6.0179408639669418e-02 -4.1827461123466492e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 27 -3.9538964629173279e-02</internalNodes>
          <leafValues>
            3.4726879000663757e-01 -7.4968926608562469e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 10 4.7501657158136368e-02</internalNodes>
          <leafValues>
            -7.6978117227554321e-02 3.5068345069885254e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 259 -5.9454172151163220e-04</internalNodes>
          <leafValues>
            1.6073931753635406e-01 -1.5279982984066010e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 10 -->
    <_>
      <maxWeakCount>58</maxWeakCount>
      <stageThreshold>-1.2862224578857422e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 882 -1.3625519350171089e-02</internalNodes>
          <leafValues>
            5.0128185749053955e-01 -1.1663150042295456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 375 -2.2920668125152588e-03</internalNodes>
          <leafValues>
            3.9538189768791199e-01 -1.3872602581977844e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 792 1.0770710650831461e-03</internalNodes>
          <leafValues>
            -1.7133137583732605e-01 3.1510788202285767e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 452 -1.2591466307640076e-02</internalNodes>
          <leafValues>
            3.9579889178276062e-01 -1.4279782772064209e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 460 -4.7927081584930420e-02</internalNodes>
          <leafValues>
            -4.9305588006973267e-01 5.6685980409383774e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 474 -2.5895023718476295e-03</internalNodes>
          <leafValues>
            1.6586430370807648e-01 -2.2577352821826935e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 112 9.8585948348045349e-02</internalNodes>
          <leafValues>
            -7.2541341185569763e-02 5.3971153497695923e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 521 7.2299325838685036e-03</internalNodes>
          <leafValues>
            7.2869211435317993e-02 -6.0541796684265137e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 202 -6.0262705665081739e-04</internalNodes>
          <leafValues>
            2.7961328625679016e-01 -1.3374039530754089e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 5.3171166218817234e-03</internalNodes>
          <leafValues>
            6.1562143266201019e-02 -5.3435516357421875e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 109 -7.3790093883872032e-03</internalNodes>
          <leafValues>
            -5.8770626783370972e-01 5.2599798887968063e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 179 2.2994203027337790e-04</internalNodes>
          <leafValues>
            -2.2165967524051666e-01 1.6663813591003418e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 366 -2.7968082576990128e-03</internalNodes>
          <leafValues>
            -4.5023602247238159e-01 6.7983791232109070e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 949 -4.4262632727622986e-03</internalNodes>
          <leafValues>
            -5.4457426071166992e-01 5.3928002715110779e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 431 -6.1236601322889328e-03</internalNodes>
          <leafValues>
            2.9386061429977417e-01 -1.0868654400110245e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 364 6.1672870069742203e-03</internalNodes>
          <leafValues>
            6.7409984767436981e-02 -4.2896196246147156e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 335 1.5454929322004318e-02</internalNodes>
          <leafValues>
            -9.3371987342834473e-02 3.2237896323204041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 285 -5.5358107201755047e-03</internalNodes>
          <leafValues>
            -6.3797932863235474e-01 4.7232467681169510e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 -5.8793288189917803e-04</internalNodes>
          <leafValues>
            2.6480975747108459e-01 -1.1852940917015076e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 203 1.2575921136885881e-03</internalNodes>
          <leafValues>
            -1.2490244954824448e-01 2.8103300929069519e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 41 3.3034523949027061e-03</internalNodes>
          <leafValues>
            6.2105692923069000e-02 -4.5968556404113770e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 45 -2.6582641527056694e-02</internalNodes>
          <leafValues>
            -5.0849837064743042e-01 5.3966015577316284e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 49 2.7427850291132927e-02</internalNodes>
          <leafValues>
            5.2529457956552505e-02 -5.3614085912704468e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 39 -2.1938718855381012e-03</internalNodes>
          <leafValues>
            -5.6713318824768066e-01 4.6497207134962082e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 926 8.5861550178378820e-04</internalNodes>
          <leafValues>
            -1.1162154376506805e-01 2.8105884790420532e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 -8.4925384726375341e-04</internalNodes>
          <leafValues>
            3.1280112266540527e-01 -1.2138028442859650e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 956 2.9905270785093307e-03</internalNodes>
          <leafValues>
            6.1607286334037781e-02 -5.1581907272338867e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 968 5.8231391012668610e-03</internalNodes>
          <leafValues>
            4.7376025468111038e-02 -5.1492005586624146e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 480 4.2811138555407524e-03</internalNodes>
          <leafValues>
            3.2761037349700928e-02 -6.7820072174072266e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 915 9.5272483304142952e-04</internalNodes>
          <leafValues>
            -1.5452747046947479e-01 1.7837351560592651e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 -2.7698231860995293e-04</internalNodes>
          <leafValues>
            1.8924367427825928e-01 -1.3868112862110138e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 370 3.0586202628910542e-03</internalNodes>
          <leafValues>
            5.3298473358154297e-02 -4.7908756136894226e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 639 2.0293965935707092e-03</internalNodes>
          <leafValues>
            3.1667634844779968e-02 -6.7199909687042236e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 639 -1.8073513638228178e-03</internalNodes>
          <leafValues>
            -6.4894622564315796e-01 3.3469315618276596e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 320 -1.1197938583791256e-03</internalNodes>
          <leafValues>
            2.2734998166561127e-01 -1.1382233351469040e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 828 1.2703117681667209e-03</internalNodes>
          <leafValues>
            -9.7680233418941498e-02 2.9997348785400391e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 835 -1.8036495894193649e-03</internalNodes>
          <leafValues>
            2.3566392064094543e-01 -1.1566326767206192e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 222 2.3318463936448097e-03</internalNodes>
          <leafValues>
            5.5787801742553711e-02 -4.4648987054824829e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 111 1.8485619220882654e-03</internalNodes>
          <leafValues>
            -1.0420991480350494e-01 2.4521166086196899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 101 8.2633290439844131e-03</internalNodes>
          <leafValues>
            5.3129263222217560e-02 -4.8460647463798523e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 760 2.7392050469643436e-05</internalNodes>
          <leafValues>
            -1.7487643659114838e-01 1.3620604574680328e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 352 2.6163433212786913e-03</internalNodes>
          <leafValues>
            -9.9586494266986847e-02 2.4075058102607727e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 94 3.6149267107248306e-03</internalNodes>
          <leafValues>
            4.2312353849411011e-02 -5.5195075273513794e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 403 1.4812931418418884e-02</internalNodes>
          <leafValues>
            -6.7619144916534424e-02 3.7573158740997314e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 814 -2.8877586591988802e-03</internalNodes>
          <leafValues>
            -5.3493702411651611e-01 5.1065266132354736e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 930 3.5591312916949391e-04</internalNodes>
          <leafValues>
            -1.2231220304965973e-01 1.9974029064178467e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 -1.0347569361329079e-02</internalNodes>
          <leafValues>
            -6.3408315181732178e-01 4.0167611092329025e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 34 -4.4028884731233120e-03</internalNodes>
          <leafValues>
            -5.1359844207763672e-01 4.3052427470684052e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 856 -1.6173283802345395e-03</internalNodes>
          <leafValues>
            1.4859439432621002e-01 -1.4985026419162750e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 996 -3.1839800067245960e-03</internalNodes>
          <leafValues>
            -4.1493499279022217e-01 6.0393124818801880e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 960 -7.9784039407968521e-03</internalNodes>
          <leafValues>
            2.8296649456024170e-01 -8.6312569677829742e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 797 2.8750954661518335e-03</internalNodes>
          <leafValues>
            -6.7822508513927460e-02 3.2967612147331238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 992 -1.1433581821620464e-03</internalNodes>
          <leafValues>
            -3.4375748038291931e-01 6.8774074316024780e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 668 1.7783213406801224e-03</internalNodes>
          <leafValues>
            -8.8273152709007263e-02 2.6904863119125366e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 670 -6.3564153388142586e-03</internalNodes>
          <leafValues>
            3.4165042638778687e-01 -7.6342806220054626e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 712 5.8753319084644318e-02</internalNodes>
          <leafValues>
            3.6884155124425888e-02 -7.0002478361129761e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 345 -1.2118986342102289e-03</internalNodes>
          <leafValues>
            1.8067996203899384e-01 -1.2888990342617035e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 268 -3.4786794334650040e-02</internalNodes>
          <leafValues>
            2.8380703926086426e-01 -1.0494612902402878e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 11 -->
    <_>
      <maxWeakCount>61</maxWeakCount>
      <stageThreshold>-1.3526766300201416e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 875 9.3241240829229355e-03</internalNodes>
          <leafValues>
            -1.1945860832929611e-01 4.8265087604522705e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 573 -4.0869116783142090e-03</internalNodes>
          <leafValues>
            2.7903670072555542e-01 -2.3448269069194794e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 676 8.3140000700950623e-02</internalNodes>
          <leafValues>
            -8.5437655448913574e-02 5.4905670881271362e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 802 2.6708254590630531e-03</internalNodes>
          <leafValues>
            -1.6097296774387360e-01 3.5868695378303528e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 75 2.2817514836788177e-03</internalNodes>
          <leafValues>
            -1.6324259340763092e-01 2.3956388235092163e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 745 6.7889376077800989e-04</internalNodes>
          <leafValues>
            -2.5205141305923462e-01 1.6190616786479950e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 811 3.1512721907347441e-03</internalNodes>
          <leafValues>
            -1.3325424492359161e-01 2.7017220854759216e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 53 5.7821646332740784e-02</internalNodes>
          <leafValues>
            -6.7158013582229614e-02 4.1875806450843811e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 442 2.8442896902561188e-02</internalNodes>
          <leafValues>
            5.5711831897497177e-02 -5.8136337995529175e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 -1.7370734130963683e-03</internalNodes>
          <leafValues>
            -6.7132610082626343e-01 3.2464105635881424e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 324 -1.9680276513099670e-02</internalNodes>
          <leafValues>
            3.9044600725173950e-01 -8.8745564222335815e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 224 1.0001409798860550e-02</internalNodes>
          <leafValues>
            -1.5947268903255463e-01 2.7087828516960144e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 1.2495646951720119e-03</internalNodes>
          <leafValues>
            8.3702936768531799e-02 -4.6324184536933899e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 144 3.0510198324918747e-02</internalNodes>
          <leafValues>
            -1.0709584504365921e-01 3.2648065686225891e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 995 -3.7916197907179594e-03</internalNodes>
          <leafValues>
            -6.1073684692382812e-01 4.7788143157958984e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 880 8.5655774455517530e-04</internalNodes>
          <leafValues>
            -2.0807541906833649e-01 1.5517778694629669e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 986 -3.2812850549817085e-03</internalNodes>
          <leafValues>
            -5.8795136213302612e-01 4.5926980674266815e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 499 3.6125673796050251e-04</internalNodes>
          <leafValues>
            -1.6806155443191528e-01 1.7441834509372711e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 591 -1.2282358948141336e-03</internalNodes>
          <leafValues>
            -4.7641313076019287e-01 5.6790668517351151e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 411 9.3263220041990280e-03</internalNodes>
          <leafValues>
            -7.4045926332473755e-02 3.7817317247390747e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 591 7.4745330493897200e-04</internalNodes>
          <leafValues>
            8.0762349069118500e-02 -3.5692575573921204e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 900 7.4315653182566166e-03</internalNodes>
          <leafValues>
            -8.5764542222023010e-02 3.2155406475067139e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 776 2.7057509869337082e-02</internalNodes>
          <leafValues>
            6.9296583533287048e-02 -4.2836430668830872e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 504 3.9283365011215210e-02</internalNodes>
          <leafValues>
            -1.0806435346603394e-01 2.9007008671760559e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 23 -3.4139624238014221e-01</internalNodes>
          <leafValues>
            5.0227731466293335e-01 -6.3795588910579681e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 502 -1.8172953277826309e-02</internalNodes>
          <leafValues>
            2.7207729220390320e-01 -1.0322675853967667e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 509 1.5265008434653282e-02</internalNodes>
          <leafValues>
            -1.0788526386022568e-01 2.4405729770660400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 -1.4973650686442852e-03</internalNodes>
          <leafValues>
            2.8644701838493347e-01 -1.0436929017305374e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 674 2.1207414101809263e-03</internalNodes>
          <leafValues>
            4.5713264495134354e-02 -6.6571021080017090e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 254 1.3393461704254150e-02</internalNodes>
          <leafValues>
            -8.4284797310829163e-02 3.6480179429054260e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 560 9.7873376216739416e-04</internalNodes>
          <leafValues>
            -1.2960052490234375e-01 2.2095513343811035e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 747 -4.9731796607375145e-03</internalNodes>
          <leafValues>
            2.7467787265777588e-01 -1.0236363112926483e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 294 -7.9883169382810593e-03</internalNodes>
          <leafValues>
            -5.3638678789138794e-01 5.3369920700788498e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 413 2.3855306208133698e-03</internalNodes>
          <leafValues>
            5.4967612028121948e-02 -4.2117682099342346e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 899 -3.0849636532366276e-03</internalNodes>
          <leafValues>
            2.6192533969879150e-01 -9.4207443296909332e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 653 4.3416069820523262e-03</internalNodes>
          <leafValues>
            -1.5543100237846375e-01 1.6663897037506104e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 451 3.8728015497326851e-03</internalNodes>
          <leafValues>
            4.9280565232038498e-02 -4.9337747693061829e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 563 1.8099667504429817e-03</internalNodes>
          <leafValues>
            4.2697191238403320e-02 -5.2748012542724609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 157 -3.3727339468896389e-03</internalNodes>
          <leafValues>
            2.0491680502891541e-01 -1.2846539914608002e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 344 3.1393815297633410e-03</internalNodes>
          <leafValues>
            -7.3090612888336182e-02 3.4941059350967407e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 3.2568261958658695e-03</internalNodes>
          <leafValues>
            4.5729346573352814e-02 -5.7302659749984741e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 853 -2.0513155031949282e-03</internalNodes>
          <leafValues>
            -5.4655516147613525e-01 3.8907390087842941e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 656 -2.7090720832347870e-03</internalNodes>
          <leafValues>
            -5.2781039476394653e-01 3.8093525916337967e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 738 -3.6282267421483994e-02</internalNodes>
          <leafValues>
            -5.8760797977447510e-01 3.4759882837533951e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 558 3.7925848737359047e-03</internalNodes>
          <leafValues>
            -8.5966393351554871e-02 2.6226586103439331e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 991 -3.7565450184047222e-03</internalNodes>
          <leafValues>
            -5.7828390598297119e-01 3.9440535008907318e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 906 -7.8137982636690140e-03</internalNodes>
          <leafValues>
            3.5042202472686768e-01 -6.6597603261470795e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 904 -3.1100357882678509e-03</internalNodes>
          <leafValues>
            1.8389418721199036e-01 -1.4107073843479156e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 449 9.1797057539224625e-03</internalNodes>
          <leafValues>
            -6.2711343169212341e-02 3.4819519519805908e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 255 -2.9698751866817474e-02</internalNodes>
          <leafValues>
            2.8956320881843567e-01 -8.5679493844509125e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 720 7.9502481967210770e-03</internalNodes>
          <leafValues>
            3.9165180176496506e-02 -6.0753583908081055e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 621 2.2064188960939646e-03</internalNodes>
          <leafValues>
            3.5431943833827972e-02 -5.5480444431304932e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 175 -3.1044434756040573e-02</internalNodes>
          <leafValues>
            -6.2628567218780518e-01 3.1049268320202827e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 0 -1.3199620880186558e-03</internalNodes>
          <leafValues>
            1.5564316511154175e-01 -1.3879336416721344e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 397 -9.6068280981853604e-04</internalNodes>
          <leafValues>
            1.9332279264926910e-01 -1.1179215461015701e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 43 7.4608568102121353e-03</internalNodes>
          <leafValues>
            5.7219974696636200e-02 -4.2135125398635864e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 293 -4.3320422992110252e-03</internalNodes>
          <leafValues>
            -6.8079024553298950e-01 2.9504306614398956e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 274 -6.5548438578844070e-03</internalNodes>
          <leafValues>
            2.9043409228324890e-01 -8.7089523673057556e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 204 4.2611984536051750e-03</internalNodes>
          <leafValues>
            -8.5929870605468750e-02 3.1930494308471680e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 635 -7.2978977113962173e-03</internalNodes>
          <leafValues>
            1.4620631933212280e-01 -1.7617914080619812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 225 -2.2543172817677259e-03</internalNodes>
          <leafValues>
            -5.9305733442306519e-01 3.9764832705259323e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 12 -->
    <_>
      <maxWeakCount>70</maxWeakCount>
      <stageThreshold>-1.3067549467086792e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 742 -5.6160744279623032e-03</internalNodes>
          <leafValues>
            4.7913768887519836e-01 -9.8717339336872101e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 536 -5.6263338774442673e-03</internalNodes>
          <leafValues>
            2.8639736771583557e-01 -1.7997759580612183e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -1.6268140170723200e-03</internalNodes>
          <leafValues>
            3.0874463915824890e-01 -1.3907180726528168e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 802 -1.3920383062213659e-03</internalNodes>
          <leafValues>
            3.2034638524055481e-01 -1.3876211643218994e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 826 3.4234612248837948e-03</internalNodes>
          <leafValues>
            -1.0860712081193924e-01 3.2174232602119446e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 525 4.3767906725406647e-02</internalNodes>
          <leafValues>
            -1.3255064189434052e-01 3.7021124362945557e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 -4.4696494005620480e-03</internalNodes>
          <leafValues>
            -4.5687621831893921e-01 8.2243621349334717e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 332 -7.1945399977266788e-03</internalNodes>
          <leafValues>
            -6.4334297180175781e-01 4.5623987913131714e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 273 6.5287351608276367e-03</internalNodes>
          <leafValues>
            -8.9336074888706207e-02 3.3727860450744629e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 771 2.8297028038650751e-03</internalNodes>
          <leafValues>
            -1.0177894681692123e-01 3.5831856727600098e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 925 1.1526069603860378e-02</internalNodes>
          <leafValues>
            7.5238041579723358e-02 -4.8319393396377563e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 4.7937319613993168e-03</internalNodes>
          <leafValues>
            5.7682428508996964e-02 -4.7086900472640991e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 395 -3.6777029745280743e-03</internalNodes>
          <leafValues>
            -4.2743790149688721e-01 7.4363298714160919e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 839 -8.0760312266647816e-04</internalNodes>
          <leafValues>
            1.4320656657218933e-01 -1.9929704070091248e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 233 3.7253312766551971e-03</internalNodes>
          <leafValues>
            5.2736207842826843e-02 -5.2105212211608887e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 416 -2.3560712113976479e-02</internalNodes>
          <leafValues>
            4.0658730268478394e-01 -7.3024936020374298e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 311 -4.5593185350298882e-03</internalNodes>
          <leafValues>
            -6.3590377569198608e-01 3.5127460956573486e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 551 -2.4863984435796738e-03</internalNodes>
          <leafValues>
            -4.5599257946014404e-01 5.3035512566566467e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 424 -2.6802124921232462e-03</internalNodes>
          <leafValues>
            1.9116453826427460e-01 -1.3404799997806549e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 -7.7647715806961060e-02</internalNodes>
          <leafValues>
            4.1297465562820435e-01 -6.3970938324928284e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 566 2.3329094983637333e-03</internalNodes>
          <leafValues>
            -1.2160944193601608e-01 2.3117628693580627e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 5 -6.6609308123588562e-03</internalNodes>
          <leafValues>
            2.2600707411766052e-01 -1.2069495767354965e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 133 -5.0821684300899506e-02</internalNodes>
          <leafValues>
            3.2217630743980408e-01 -7.6335281133651733e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 537 -7.0379404351115227e-03</internalNodes>
          <leafValues>
            1.8399104475975037e-01 -1.4812190830707550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 134 -3.3276520669460297e-02</internalNodes>
          <leafValues>
            -6.0358065366744995e-01 3.5330448299646378e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 392 7.5909225270152092e-03</internalNodes>
          <leafValues>
            3.1779482960700989e-02 -6.4767998456954956e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 613 -5.6639023125171661e-02</internalNodes>
          <leafValues>
            -4.6455994248390198e-01 4.6072337776422501e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 3.7777128163725138e-03</internalNodes>
          <leafValues>
            5.7451672852039337e-02 -3.7793967127799988e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 8.9145395904779434e-03</internalNodes>
          <leafValues>
            -7.5942978262901306e-02 3.1487807631492615e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 841 -1.4818884432315826e-02</internalNodes>
          <leafValues>
            2.7122247219085693e-01 -9.8314434289932251e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 381 -5.5922558531165123e-03</internalNodes>
          <leafValues>
            -6.4762401580810547e-01 4.1314963251352310e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 595 3.1491921981796622e-04</internalNodes>
          <leafValues>
            -1.4864055812358856e-01 1.4411780238151550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 136 -5.7063563726842403e-03</internalNodes>
          <leafValues>
            -4.6024248003959656e-01 4.7999884933233261e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 -1.2257394846528769e-03</internalNodes>
          <leafValues>
            3.2288366556167603e-01 -7.0425607264041901e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 775 -1.6291948035359383e-02</internalNodes>
          <leafValues>
            2.7573275566101074e-01 -8.3055868744850159e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 156 -8.1639690324664116e-04</internalNodes>
          <leafValues>
            1.7044979333877563e-01 -1.4129574596881866e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 975 5.1114819943904877e-03</internalNodes>
          <leafValues>
            3.3882420510053635e-02 -6.9941717386245728e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 977 -2.8371806256473064e-03</internalNodes>
          <leafValues>
            -3.7707236409187317e-01 5.7759616523981094e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 772 5.3479857742786407e-03</internalNodes>
          <leafValues>
            4.1541736572980881e-02 -4.8687714338302612e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 1.1360908392816782e-03</internalNodes>
          <leafValues>
            -7.8717894852161407e-02 2.9692038893699646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 947 1.4100213302299380e-03</internalNodes>
          <leafValues>
            4.3843001127243042e-02 -5.1339787244796753e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 387 8.7079760851338506e-04</internalNodes>
          <leafValues>
            -9.8695866763591766e-02 2.2730629146099091e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 211 -5.4065873846411705e-03</internalNodes>
          <leafValues>
            -6.3011974096298218e-01 3.7802927196025848e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 816 -1.6894804313778877e-02</internalNodes>
          <leafValues>
            -5.0091201066970825e-01 3.5215172916650772e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 766 1.4164673630148172e-03</internalNodes>
          <leafValues>
            -8.8441111147403717e-02 2.4102251231670380e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 704 -1.1464871931821108e-03</internalNodes>
          <leafValues>
            1.9273723661899567e-01 -1.1090471595525742e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 861 -3.2706123311072588e-03</internalNodes>
          <leafValues>
            -4.5202803611755371e-01 4.7059688717126846e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 70 1.1416582390666008e-02</internalNodes>
          <leafValues>
            2.6714416220784187e-02 -6.9660711288452148e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 310 2.7643535286188126e-03</internalNodes>
          <leafValues>
            4.7252438962459564e-02 -3.9458727836608887e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 435 2.4567130021750927e-03</internalNodes>
          <leafValues>
            -7.5188823044300079e-02 2.9944056272506714e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 441 -7.3516201227903366e-03</internalNodes>
          <leafValues>
            2.8476437926292419e-01 -9.2367134988307953e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 662 -4.3670929968357086e-02</internalNodes>
          <leafValues>
            -6.8588620424270630e-01 3.3353023231029510e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 138 -6.4992159605026245e-02</internalNodes>
          <leafValues>
            -7.9678738117218018e-01 2.0331909880042076e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 286 -1.1700032278895378e-02</internalNodes>
          <leafValues>
            -6.1183351278305054e-01 2.7328895404934883e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 589 3.0743866227567196e-03</internalNodes>
          <leafValues>
            -7.7295452356338501e-02 2.6685911417007446e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 584 -1.5546076931059361e-02</internalNodes>
          <leafValues>
            -5.5246621370315552e-01 4.0912687778472900e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 40 6.5568592399358749e-03</internalNodes>
          <leafValues>
            -1.0432150214910507e-01 1.9379787147045135e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 29 -8.0047458410263062e-02</internalNodes>
          <leafValues>
            3.9228948950767517e-01 -5.2565738558769226e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 227 1.5684183686971664e-02</internalNodes>
          <leafValues>
            -1.1151826381683350e-01 1.8633136153221130e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 546 2.3603178560733795e-03</internalNodes>
          <leafValues>
            -1.0219112038612366e-01 2.0333246886730194e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 585 -3.5169085022062063e-03</internalNodes>
          <leafValues>
            2.7427124977111816e-01 -8.6362943053245544e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 476 9.4871241599321365e-03</internalNodes>
          <leafValues>
            3.5626750439405441e-02 -6.2631088495254517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 629 -9.3261618167161942e-03</internalNodes>
          <leafValues>
            -7.1806514263153076e-01 2.4241568520665169e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 666 -6.3302312046289444e-03</internalNodes>
          <leafValues>
            2.1094995737075806e-01 -9.2475786805152893e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 598 -2.8244811110198498e-03</internalNodes>
          <leafValues>
            2.6596403121948242e-01 -8.0099694430828094e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 145 -1.1591307818889618e-02</internalNodes>
          <leafValues>
            2.3619163036346436e-01 -8.5169024765491486e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 117 2.1401243284344673e-03</internalNodes>
          <leafValues>
            -1.0995808988809586e-01 2.1230246126651764e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 562 4.2046746239066124e-03</internalNodes>
          <leafValues>
            3.6688093096017838e-02 -6.1654287576675415e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 605 1.1085141450166702e-03</internalNodes>
          <leafValues>
            -8.0656312406063080e-02 2.7754181623458862e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 829 -8.2805287092924118e-03</internalNodes>
          <leafValues>
            -6.5883606672286987e-01 3.6048211157321930e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 13 -->
    <_>
      <maxWeakCount>70</maxWeakCount>
      <stageThreshold>-1.2368309497833252e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 716 -3.3105849288403988e-03</internalNodes>
          <leafValues>
            5.0566112995147705e-01 -8.2956805825233459e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 190 4.5855166390538216e-03</internalNodes>
          <leafValues>
            -1.3226345181465149e-01 3.9034894108772278e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 576 -2.6665716432034969e-03</internalNodes>
          <leafValues>
            2.7508354187011719e-01 -1.3807572424411774e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 734 1.8106825649738312e-02</internalNodes>
          <leafValues>
            -1.2738862633705139e-01 3.5449108481407166e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 830 -5.7813120074570179e-03</internalNodes>
          <leafValues>
            2.7463605999946594e-01 -1.2951526045799255e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 379 8.9321136474609375e-03</internalNodes>
          <leafValues>
            4.8491790890693665e-02 -5.8104276657104492e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 17 6.2806839123368263e-03</internalNodes>
          <leafValues>
            -1.3215491175651550e-01 2.1852293610572815e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 9 -4.3670572340488434e-02</internalNodes>
          <leafValues>
            3.8786840438842773e-01 -7.4191503226757050e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 -6.2309622764587402e-02</internalNodes>
          <leafValues>
            3.3408007025718689e-01 -8.7087221443653107e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 686 -3.2859744969755411e-03</internalNodes>
          <leafValues>
            3.3486780524253845e-01 -8.9008949697017670e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 346 -3.9627305231988430e-03</internalNodes>
          <leafValues>
            2.6155433058738708e-01 -9.5614455640316010e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 1.0877416934818029e-03</internalNodes>
          <leafValues>
            -1.4199735224246979e-01 1.8414285778999329e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 5.4819821380078793e-03</internalNodes>
          <leafValues>
            7.4260123074054718e-02 -5.6989872455596924e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 916 4.9011572264134884e-04</internalNodes>
          <leafValues>
            -1.9576059281826019e-01 1.3506270945072174e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 911 -7.7052684500813484e-03</internalNodes>
          <leafValues>
            -5.0443643331527710e-01 6.1383318156003952e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 164 4.8691947013139725e-03</internalNodes>
          <leafValues>
            4.3469026684761047e-02 -5.2802342176437378e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 344 2.4673391599208117e-03</internalNodes>
          <leafValues>
            -8.9178681373596191e-02 3.0606627464294434e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 172 -3.6682826466858387e-03</internalNodes>
          <leafValues>
            -6.5514552593231201e-01 4.7427203506231308e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 2.5194899644702673e-03</internalNodes>
          <leafValues>
            4.9365170300006866e-02 -4.0812951326370239e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 531 5.8970693498849869e-03</internalNodes>
          <leafValues>
            3.5579398274421692e-02 -6.4191317558288574e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 1.7767311073839664e-03</internalNodes>
          <leafValues>
            -8.6629316210746765e-02 2.7705979347229004e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 885 4.0457276627421379e-03</internalNodes>
          <leafValues>
            5.6002113968133926e-02 -4.7005215287208557e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 522 3.2862280495464802e-03</internalNodes>
          <leafValues>
            -1.2930884957313538e-01 2.0613414049148560e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 322 1.4660503948107362e-03</internalNodes>
          <leafValues>
            -9.9395424127578735e-02 3.3950179815292358e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 266 1.9015703350305557e-02</internalNodes>
          <leafValues>
            6.0197159647941589e-02 -5.1893943548202515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 102 -7.1178808808326721e-02</internalNodes>
          <leafValues>
            -4.3668299913406372e-01 4.7340013086795807e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -4.6305771684274077e-04</internalNodes>
          <leafValues>
            1.4736598730087280e-01 -1.5406486392021179e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 298 -4.7644632868468761e-03</internalNodes>
          <leafValues>
            -5.0336647033691406e-01 4.4053792953491211e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 761 -8.5318256169557571e-03</internalNodes>
          <leafValues>
            -5.9967356920242310e-01 3.2567754387855530e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 713 -2.7496295515447855e-03</internalNodes>
          <leafValues>
            1.3502316176891327e-01 -1.6025592386722565e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 607 4.2666587978601456e-03</internalNodes>
          <leafValues>
            2.5802688673138618e-02 -7.8170543909072876e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 216 -2.9856398701667786e-02</internalNodes>
          <leafValues>
            2.4982222914695740e-01 -8.8180385529994965e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 226 2.2136634215712547e-03</internalNodes>
          <leafValues>
            -1.4314906299114227e-01 1.6945528984069824e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 640 1.6336794942617416e-02</internalNodes>
          <leafValues>
            4.6008959412574768e-02 -4.9338266253471375e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 459 7.9861842095851898e-03</internalNodes>
          <leafValues>
            -1.1460029333829880e-01 1.9282819330692291e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 650 -1.7455726629123092e-03</internalNodes>
          <leafValues>
            1.7520657181739807e-01 -1.2269173562526703e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 124 -6.2451506964862347e-03</internalNodes>
          <leafValues>
            -4.5638361573219299e-01 4.8106320202350616e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 406 8.5668899118900299e-03</internalNodes>
          <leafValues>
            -8.0403454601764679e-02 3.0411326885223389e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 974 8.6863581091165543e-03</internalNodes>
          <leafValues>
            3.4176670014858246e-02 -7.3028022050857544e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 36 1.0814646258950233e-02</internalNodes>
          <leafValues>
            2.5131458416581154e-02 -6.7325627803802490e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 709 4.4222913682460785e-02</internalNodes>
          <leafValues>
            3.9326712489128113e-02 -5.1067680120468140e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 903 3.7128489930182695e-03</internalNodes>
          <leafValues>
            -1.3248492777347565e-01 1.6692358255386353e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 129 -4.6475054696202278e-03</internalNodes>
          <leafValues>
            1.7683532834053040e-01 -1.2570241093635559e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 291 4.2433524504303932e-03</internalNodes>
          <leafValues>
            3.6985948681831360e-02 -5.8369445800781250e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 -5.1774000748991966e-03</internalNodes>
          <leafValues>
            5.1487326622009277e-01 -4.1473735123872757e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 855 4.2645614594221115e-03</internalNodes>
          <leafValues>
            3.7253957241773605e-02 -5.7676959037780762e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 83 4.8632645048201084e-03</internalNodes>
          <leafValues>
            -6.7035257816314697e-02 3.1131938099861145e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 250 2.6089766994118690e-02</internalNodes>
          <leafValues>
            -8.2920446991920471e-02 3.0445784330368042e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 625 -1.9001008477061987e-03</internalNodes>
          <leafValues>
            -4.3419414758682251e-01 4.6812325716018677e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 891 -6.0952613130211830e-03</internalNodes>
          <leafValues>
            -5.1850622892379761e-01 3.6754775792360306e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 564 1.2120242230594158e-02</internalNodes>
          <leafValues>
            -7.4773810803890228e-02 2.6738941669464111e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 817 -1.8978580832481384e-02</internalNodes>
          <leafValues>
            2.5657230615615845e-01 -8.0304212868213654e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 338 4.3438978493213654e-02</internalNodes>
          <leafValues>
            -6.2818735837936401e-02 3.2261833548545837e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 773 9.4384723342955112e-04</internalNodes>
          <leafValues>
            -9.8582215607166290e-02 2.2370135784149170e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 519 -4.1803726926445961e-03</internalNodes>
          <leafValues>
            -4.9802374839782715e-01 4.3809909373521805e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 195 -9.7246468067169189e-03</internalNodes>
          <leafValues>
            2.2823798656463623e-01 -9.8547600209712982e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 658 2.7193846181035042e-03</internalNodes>
          <leafValues>
            -9.1188244521617889e-02 2.2684387862682343e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 174 6.2224082648754120e-03</internalNodes>
          <leafValues>
            3.2258503139019012e-02 -6.0108250379562378e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 77 -4.8602908849716187e-01</internalNodes>
          <leafValues>
            6.3337916135787964e-01 -3.3006772398948669e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 550 -5.3604291751980782e-03</internalNodes>
          <leafValues>
            2.9434949159622192e-01 -6.1312302947044373e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 541 5.5021280422806740e-03</internalNodes>
          <leafValues>
            4.1839476674795151e-02 -4.5681878924369812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 326 -1.3823953922837973e-03</internalNodes>
          <leafValues>
            1.6067574918270111e-01 -1.1796293407678604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 514 2.0954519510269165e-02</internalNodes>
          <leafValues>
            -5.7253565639257431e-02 3.3830171823501587e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 409 7.4234008789062500e-03</internalNodes>
          <leafValues>
            -7.4798591434955597e-02 2.6430690288543701e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 578 2.1767318248748779e-03</internalNodes>
          <leafValues>
            -8.0530151724815369e-02 2.5947657227516174e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 623 1.8930230289697647e-03</internalNodes>
          <leafValues>
            -8.1788897514343262e-02 2.2988820075988770e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 533 6.9275917485356331e-03</internalNodes>
          <leafValues>
            2.6962997391819954e-02 -7.6910203695297241e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 334 6.7140227183699608e-03</internalNodes>
          <leafValues>
            2.3244854062795639e-02 -6.8406605720520020e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 632 -3.4494437277317047e-02</internalNodes>
          <leafValues>
            -6.5257686376571655e-01 2.4584138765931129e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 787 1.9636256620287895e-03</internalNodes>
          <leafValues>
            -9.1118760406970978e-02 2.0629465579986572e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 14 -->
    <_>
      <maxWeakCount>80</maxWeakCount>
      <stageThreshold>-1.3304495811462402e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 572 -9.1053368523716927e-03</internalNodes>
          <leafValues>
            4.8031216859817505e-01 -9.3147851526737213e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 715 -2.1384856663644314e-03</internalNodes>
          <leafValues>
            3.4027156233787537e-01 -1.4834050834178925e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 953 1.2453617528080940e-02</internalNodes>
          <leafValues>
            -8.0359503626823425e-02 4.7585478425025940e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 198 5.0965799018740654e-03</internalNodes>
          <leafValues>
            -1.6364066302776337e-01 2.9590085148811340e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 477 -3.1894792336970568e-03</internalNodes>
          <leafValues>
            1.7039565742015839e-01 -2.1295401453971863e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 314 -1.4799979981034994e-03</internalNodes>
          <leafValues>
            -4.1050529479980469e-01 5.3783610463142395e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 66 6.0710287652909756e-03</internalNodes>
          <leafValues>
            -1.5162153542041779e-01 1.8406888842582703e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 401 4.3081510812044144e-03</internalNodes>
          <leafValues>
            5.0293717533349991e-02 -4.6324169635772705e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 970 1.8933035898953676e-03</internalNodes>
          <leafValues>
            6.5655551850795746e-02 -3.9198148250579834e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 782 -1.6021143645048141e-02</internalNodes>
          <leafValues>
            2.2748421132564545e-01 -1.0609938949346542e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 928 -8.9298677630722523e-04</internalNodes>
          <leafValues>
            3.1164079904556274e-01 -1.1380065232515335e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 888 -1.4284942299127579e-03</internalNodes>
          <leafValues>
            2.7966943383216858e-01 -9.6580952405929565e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 822 2.5015190243721008e-02</internalNodes>
          <leafValues>
            4.2534209787845612e-02 -6.2623745203018188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 583 -2.8645459096878767e-03</internalNodes>
          <leafValues>
            -4.1426309943199158e-01 5.1780503243207932e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 902 3.2044243998825550e-03</internalNodes>
          <leafValues>
            -1.1883606761693954e-01 1.9546063244342804e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 319 -1.0433372110128403e-02</internalNodes>
          <leafValues>
            2.6159819960594177e-01 -9.3164652585983276e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 287 -9.7299478948116302e-03</internalNodes>
          <leafValues>
            -4.9464005231857300e-01 5.0998747348785400e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 206 -2.1688457578420639e-02</internalNodes>
          <leafValues>
            5.6923902034759521e-01 -4.9958106130361557e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 38 -2.9492072761058807e-02</internalNodes>
          <leafValues>
            -6.1336356401443481e-01 4.7003138810396194e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 35 -2.4866596795618534e-03</internalNodes>
          <leafValues>
            -3.9986124634742737e-01 5.7781789451837540e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 965 4.0488247759640217e-03</internalNodes>
          <leafValues>
            4.6429801732301712e-02 -4.4500553607940674e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 735 -9.3909690622240305e-04</internalNodes>
          <leafValues>
            2.4617424607276917e-01 -9.0848781168460846e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 989 -5.2673118188977242e-03</internalNodes>
          <leafValues>
            -6.4129960536956787e-01 3.5207435488700867e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 806 -6.1755320057272911e-03</internalNodes>
          <leafValues>
            1.7039734125137329e-01 -1.3195209205150604e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 1.5832348726689816e-03</internalNodes>
          <leafValues>
            -9.2635877430438995e-02 2.5755262374877930e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 914 2.8633023612201214e-03</internalNodes>
          <leafValues>
            5.0923369824886322e-02 -4.6171438694000244e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 12 -2.3722708225250244e-02</internalNodes>
          <leafValues>
            -4.5609694719314575e-01 4.3677136301994324e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 419 5.8846692554652691e-03</internalNodes>
          <leafValues>
            5.1512561738491058e-02 -4.4899132847785950e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 201 -8.2513026427477598e-04</internalNodes>
          <leafValues>
            2.4914309382438660e-01 -8.9795768260955811e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 690 -2.9888928402215242e-03</internalNodes>
          <leafValues>
            -4.0133482217788696e-01 5.5449619889259338e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 237 1.8384978175163269e-02</internalNodes>
          <leafValues>
            4.9513496458530426e-02 -4.2024865746498108e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 947 -2.4238843470811844e-03</internalNodes>
          <leafValues>
            -6.7325645685195923e-01 2.8972415253520012e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 724 8.1563717685639858e-04</internalNodes>
          <leafValues>
            -1.4400914311408997e-01 1.5184181928634644e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 315 2.1788734011352062e-03</internalNodes>
          <leafValues>
            -8.2650899887084961e-02 2.5927037000656128e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 3.7263201083987951e-03</internalNodes>
          <leafValues>
            -6.3213117420673370e-02 3.8062268495559692e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 631 3.0819473322480917e-03</internalNodes>
          <leafValues>
            3.9066124707460403e-02 -6.2055569887161255e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 691 2.7417289093136787e-03</internalNodes>
          <leafValues>
            3.2166294753551483e-02 -5.6402361392974854e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 581 -3.8205389864742756e-03</internalNodes>
          <leafValues>
            2.5668358802795410e-01 -7.9121366143226624e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 61 -1.2516178190708160e-02</internalNodes>
          <leafValues>
            -7.0402121543884277e-01 3.2493114471435547e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 60 4.6941628679633141e-03</internalNodes>
          <leafValues>
            4.7352086752653122e-02 -4.0129581093788147e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 483 5.0501096993684769e-03</internalNodes>
          <leafValues>
            -1.0563907027244568e-01 2.3647888004779816e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 497 1.5111428685486317e-02</internalNodes>
          <leafValues>
            -6.7443214356899261e-02 2.7579694986343384e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 7.4835181236267090e-02</internalNodes>
          <leafValues>
            -6.2918186187744141e-02 3.6493194103240967e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 498 1.3086002320051193e-02</internalNodes>
          <leafValues>
            2.9699811711907387e-02 -7.4420636892318726e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 778 -5.4838880896568298e-03</internalNodes>
          <leafValues>
            2.2497597336769104e-01 -8.8018722832202911e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 261 3.3699360210448503e-03</internalNodes>
          <leafValues>
            -6.9213069975376129e-02 2.9263094067573547e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 118 7.7881952747702599e-03</internalNodes>
          <leafValues>
            5.8034870773553848e-02 -3.9803403615951538e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 421 -1.9298251718282700e-02</internalNodes>
          <leafValues>
            2.1273820102214813e-01 -9.6075013279914856e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 440 1.3059679418802261e-02</internalNodes>
          <leafValues>
            4.0989801287651062e-02 -4.9787399172782898e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 510 -2.2303011268377304e-02</internalNodes>
          <leafValues>
            -6.5915608406066895e-01 2.7258813381195068e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 260 -5.2872681990265846e-03</internalNodes>
          <leafValues>
            2.9461637139320374e-01 -6.9564543664455414e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 464 6.0780980857089162e-04</internalNodes>
          <leafValues>
            -9.5468334853649139e-02 2.0951601862907410e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 444 4.8917778767645359e-03</internalNodes>
          <leafValues>
            3.9317954331636429e-02 -5.3803342580795288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 238 -1.0402110219001770e-01</internalNodes>
          <leafValues>
            5.4199391603469849e-01 -3.9763871580362320e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 687 3.8908584974706173e-03</internalNodes>
          <leafValues>
            3.8185238838195801e-02 -5.3280067443847656e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 353 8.0125425010919571e-03</internalNodes>
          <leafValues>
            -7.8310973942279816e-02 2.4926608800888062e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 954 -3.4356187097728252e-03</internalNodes>
          <leafValues>
            2.3415692150592804e-01 -9.2279240489006042e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 896 -5.2030328661203384e-03</internalNodes>
          <leafValues>
            -5.0255048274993896e-01 4.4738721102476120e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 555 -5.5568795651197433e-03</internalNodes>
          <leafValues>
            2.8329169750213623e-01 -7.0860259234905243e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 627 -7.6205702498555183e-03</internalNodes>
          <leafValues>
            2.5350978970527649e-01 -7.2612494230270386e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 309 2.7379104495048523e-01</internalNodes>
          <leafValues>
            -5.6398060172796249e-02 3.6085364222526550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 622 7.3067229241132736e-03</internalNodes>
          <leafValues>
            -6.2759615480899811e-02 3.1996127963066101e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 415 3.2574313227087259e-03</internalNodes>
          <leafValues>
            4.1181974112987518e-02 -4.9355933070182800e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 57 -1.2764024734497070e-01</internalNodes>
          <leafValues>
            2.5147503614425659e-01 -7.5440123677253723e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 530 -3.2227888703346252e-02</internalNodes>
          <leafValues>
            3.9548832178115845e-01 -4.7284111380577087e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 764 2.3350853472948074e-02</internalNodes>
          <leafValues>
            -7.2977773845195770e-02 2.5172060728073120e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 26 2.7610745746642351e-05</internalNodes>
          <leafValues>
            -1.3625738024711609e-01 1.3250400125980377e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 808 6.9611091166734695e-03</internalNodes>
          <leafValues>
            2.9794082045555115e-02 -5.8855760097503662e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 -9.9057564511895180e-04</internalNodes>
          <leafValues>
            2.5895762443542480e-01 -7.1211874485015869e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 -3.7965672090649605e-03</internalNodes>
          <leafValues>
            -6.4451014995574951e-01 3.5450231283903122e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 346 3.9518065750598907e-03</internalNodes>
          <leafValues>
            -6.3615679740905762e-02 3.0333930253982544e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 282 -5.4976264946162701e-03</internalNodes>
          <leafValues>
            -4.3285435438156128e-01 4.7526597976684570e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 721 7.1266246959567070e-03</internalNodes>
          <leafValues>
            -6.6810697317123413e-02 2.8491511940956116e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 912 -3.0366722494363785e-03</internalNodes>
          <leafValues>
            -4.3046197295188904e-01 4.4313102960586548e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 714 -1.7097850795835257e-03</internalNodes>
          <leafValues>
            2.5873449444770813e-01 -7.3857538402080536e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 702 -4.4310283847153187e-03</internalNodes>
          <leafValues>
            2.1451152861118317e-01 -8.7626561522483826e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 47 -3.9760642684996128e-03</internalNodes>
          <leafValues>
            -4.6889033913612366e-01 3.8441929966211319e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 683 -2.9741778969764709e-02</internalNodes>
          <leafValues>
            -5.5860131978988647e-01 3.0309556052088737e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 13 1.3289751112461090e-01</internalNodes>
          <leafValues>
            2.8634676709771156e-02 -5.6014162302017212e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 386 -1.1272695846855640e-03</internalNodes>
          <leafValues>
            1.7104774713516235e-01 -1.0818520933389664e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 15 -->
    <_>
      <maxWeakCount>83</maxWeakCount>
      <stageThreshold>-1.2789946794509888e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 649 1.3820428401231766e-02</internalNodes>
          <leafValues>
            -1.0330537706613541e-01 4.5001628994941711e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 834 -1.0161036625504494e-02</internalNodes>
          <leafValues>
            3.2188063859939575e-01 -1.5805941820144653e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 398 -3.8372592534869909e-03</internalNodes>
          <leafValues>
            3.2943242788314819e-01 -1.1501405388116837e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 769 3.4624878317117691e-02</internalNodes>
          <leafValues>
            -9.8698168992996216e-02 5.4050970077514648e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 437 5.7967011816799641e-03</internalNodes>
          <leafValues>
            -1.1608023941516876e-01 2.8170758485794067e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 754 4.7825248911976814e-03</internalNodes>
          <leafValues>
            -1.3033217191696167e-01 2.4669390916824341e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 74 7.1141775697469711e-04</internalNodes>
          <leafValues>
            -2.0435671508312225e-01 1.1761441081762314e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 22 -2.9168082401156425e-02</internalNodes>
          <leafValues>
            -6.2692928314208984e-01 5.5113222450017929e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 796 2.1553519181907177e-03</internalNodes>
          <leafValues>
            5.3858544677495956e-02 -4.2096143960952759e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 -2.1254396997392178e-03</internalNodes>
          <leafValues>
            4.2603659629821777e-01 -5.0405498594045639e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 8.4234733367338777e-04</internalNodes>
          <leafValues>
            -9.3583315610885620e-02 2.6316204667091370e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 948 -1.6576268244534731e-03</internalNodes>
          <leafValues>
            -3.5802370309829712e-01 6.8603202700614929e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 554 6.5620511770248413e-02</internalNodes>
          <leafValues>
            -6.4758449792861938e-02 3.8339248299598694e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 361 -1.8485928885638714e-03</internalNodes>
          <leafValues>
            1.7337062954902649e-01 -1.3676019012928009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 305 -1.8170465528964996e-01</internalNodes>
          <leafValues>
            4.0350264310836792e-01 -5.3196940571069717e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 848 -3.4317909739911556e-03</internalNodes>
          <leafValues>
            -5.2157330513000488e-01 4.6489212661981583e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 800 -2.7482535224407911e-03</internalNodes>
          <leafValues>
            -5.1078474521636963e-01 4.3557438999414444e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 -4.7894287854433060e-03</internalNodes>
          <leafValues>
            3.4981805086135864e-01 -6.5036587417125702e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 706 -3.3211666159331799e-03</internalNodes>
          <leafValues>
            2.1143883466720581e-01 -1.1754662543535233e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 677 3.5642951726913452e-02</internalNodes>
          <leafValues>
            3.7131600081920624e-02 -6.2165355682373047e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 481 -3.1561930663883686e-03</internalNodes>
          <leafValues>
            -4.2197883129119873e-01 4.7645546495914459e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 872 5.2224877290427685e-03</internalNodes>
          <leafValues>
            -1.0117106884717941e-01 2.1957167983055115e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 140 2.5758458301424980e-02</internalNodes>
          <leafValues>
            -9.6981137990951538e-02 3.0423089861869812e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 567 2.8883803170174360e-03</internalNodes>
          <leafValues>
            4.4947806745767593e-02 -5.5540132522583008e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 484 2.6014349423348904e-03</internalNodes>
          <leafValues>
            4.5947834849357605e-02 -4.1711980104446411e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 257 -7.8792509157210588e-04</internalNodes>
          <leafValues>
            1.5732656419277191e-01 -1.2769798934459686e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 252 4.2199464514851570e-03</internalNodes>
          <leafValues>
            -9.4008974730968475e-02 2.6868444681167603e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 571 -2.4246796965599060e-03</internalNodes>
          <leafValues>
            -4.9610009789466858e-01 4.6141009777784348e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 465 -1.8996626604348421e-03</internalNodes>
          <leafValues>
            2.6260954141616821e-01 -8.5721127688884735e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 945 1.8048105994239450e-03</internalNodes>
          <leafValues>
            7.1231566369533539e-02 -3.2751160860061646e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 249 -5.6593962945044041e-03</internalNodes>
          <leafValues>
            -5.0264769792556763e-01 4.0275387465953827e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 940 -3.4701074473559856e-03</internalNodes>
          <leafValues>
            -4.9033272266387939e-01 3.6995064467191696e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 766 1.1992279905825853e-03</internalNodes>
          <leafValues>
            -9.3982182443141937e-02 2.2527951002120972e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 528 -3.3614276908338070e-03</internalNodes>
          <leafValues>
            1.5591301023960114e-01 -1.3875743746757507e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 758 9.2923380434513092e-03</internalNodes>
          <leafValues>
            2.8368480503559113e-02 -6.3946157693862915e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 98 -1.6806223988533020e-01</internalNodes>
          <leafValues>
            -6.3519150018692017e-01 2.4432161822915077e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 614 -1.5483988681808114e-03</internalNodes>
          <leafValues>
            -4.9389392137527466e-01 3.4452050924301147e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 961 7.9401559196412563e-04</internalNodes>
          <leafValues>
            -1.6395612061023712e-01 1.1427336186170578e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 245 -5.3670424968004227e-03</internalNodes>
          <leafValues>
            -5.4615026712417603e-01 3.2274313271045685e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 923 -5.1019818056374788e-04</internalNodes>
          <leafValues>
            1.4040225744247437e-01 -1.2673649191856384e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 846 -9.6546392887830734e-04</internalNodes>
          <leafValues>
            2.3117446899414062e-01 -7.7826015651226044e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 994 -9.7423873376101255e-04</internalNodes>
          <leafValues>
            -4.0673121809959412e-01 4.6749390661716461e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 970 -4.7841384075582027e-03</internalNodes>
          <leafValues>
            -5.0288796424865723e-01 3.4186109900474548e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 89 6.8537802435457706e-03</internalNodes>
          <leafValues>
            5.0501946359872818e-02 -3.5414797067642212e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 651 4.1695050895214081e-03</internalNodes>
          <leafValues>
            -6.8471699953079224e-02 2.8334242105484009e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 2.6521178369875997e-05</internalNodes>
          <leafValues>
            -1.7646598815917969e-01 1.0057727992534637e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 674 -1.8193974392488599e-03</internalNodes>
          <leafValues>
            -5.2059328556060791e-01 3.4266594797372818e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 1.1680822353810072e-03</internalNodes>
          <leafValues>
            -7.5169444084167480e-02 2.3740953207015991e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 -5.8111123507842422e-04</internalNodes>
          <leafValues>
            2.4673853814601898e-01 -8.9036554098129272e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 789 5.5753946304321289e-02</internalNodes>
          <leafValues>
            -4.8898559063673019e-02 3.7110447883605957e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 388 -6.0947462916374207e-03</internalNodes>
          <leafValues>
            -4.8019152879714966e-01 3.6990296095609665e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 988 3.3249799162149429e-03</internalNodes>
          <leafValues>
            3.2017692923545837e-02 -4.8544195294380188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 586 -1.1994136497378349e-02</internalNodes>
          <leafValues>
            2.7767661213874817e-01 -6.2677264213562012e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 940 1.9462420605123043e-03</internalNodes>
          <leafValues>
            5.7167824357748032e-02 -3.2460683584213257e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 482 -3.5742400214076042e-03</internalNodes>
          <leafValues>
            2.1856486797332764e-01 -7.7333562076091766e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 543 3.4013153053820133e-03</internalNodes>
          <leafValues>
            -9.4114005565643311e-02 2.3269242048263550e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 859 6.4494553953409195e-03</internalNodes>
          <leafValues>
            3.4765381366014481e-02 -5.1627504825592041e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 163 -1.2767435982823372e-02</internalNodes>
          <leafValues>
            2.5566741824150085e-01 -6.7411571741104126e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 230 2.2043818607926369e-03</internalNodes>
          <leafValues>
            -1.3278621435165405e-01 1.7942063510417938e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 229 -4.0757502429187298e-03</internalNodes>
          <leafValues>
            -3.8042715191841125e-01 4.4863421469926834e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 730 2.2066584788262844e-03</internalNodes>
          <leafValues>
            -7.0331946015357971e-02 2.5572371482849121e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 700 2.2714279592037201e-02</internalNodes>
          <leafValues>
            4.1653785854578018e-02 -4.4101753830909729e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 749 -1.1373223736882210e-02</internalNodes>
          <leafValues>
            3.2443967461585999e-01 -5.8059785515069962e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 835 1.8165379296988249e-03</internalNodes>
          <leafValues>
            -7.2351627051830292e-02 2.2953742742538452e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 235 -2.8745923191308975e-03</internalNodes>
          <leafValues>
            -3.9090758562088013e-01 4.6148840337991714e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 673 -5.7676057331264019e-03</internalNodes>
          <leafValues>
            2.4503223598003387e-01 -7.2128646075725555e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 177 1.2852130457758904e-02</internalNodes>
          <leafValues>
            -1.1143829673528671e-01 1.6758553683757782e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 141 -4.2651765048503876e-02</internalNodes>
          <leafValues>
            2.3846423625946045e-01 -7.9255387187004089e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 24 -6.8766735494136810e-03</internalNodes>
          <leafValues>
            -3.9145267009735107e-01 5.2240811288356781e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 15 -1.5351611375808716e-01</internalNodes>
          <leafValues>
            -5.4598790407180786e-01 2.9950620606541634e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 280 -1.7586871981620789e-02</internalNodes>
          <leafValues>
            2.4160921573638916e-01 -7.7404774725437164e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 557 2.8469474054872990e-03</internalNodes>
          <leafValues>
            -7.1562752127647400e-02 2.3895153403282166e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 493 -2.6379337534308434e-02</internalNodes>
          <leafValues>
            2.7370086312294006e-01 -6.5483018755912781e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 759 -6.6346197854727507e-04</internalNodes>
          <leafValues>
            1.7174075543880463e-01 -1.0841262340545654e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 736 1.4637422282248735e-03</internalNodes>
          <leafValues>
            -1.1365657299757004e-01 1.6123561561107635e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 569 -1.3798776781186461e-03</internalNodes>
          <leafValues>
            2.3192690312862396e-01 -7.5626462697982788e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 516 -6.8256547674536705e-03</internalNodes>
          <leafValues>
            2.4984428286552429e-01 -7.2457753121852875e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 312 -9.0181883424520493e-03</internalNodes>
          <leafValues>
            2.0358866453170776e-01 -9.5499873161315918e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 3.1383798923343420e-03</internalNodes>
          <leafValues>
            4.0804021060466766e-02 -4.9618390202522278e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 171 -1.8526764586567879e-02</internalNodes>
          <leafValues>
            2.2743205726146698e-01 -8.6628310382366180e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 594 -2.2562327794730663e-03</internalNodes>
          <leafValues>
            -3.2850387692451477e-01 5.9250634163618088e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 432 -4.1183121502399445e-03</internalNodes>
          <leafValues>
            -5.0281947851181030e-01 3.2455049455165863e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 96 4.8136096447706223e-03</internalNodes>
          <leafValues>
            3.1708184629678726e-02 -4.9248033761978149e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 16 -->
    <_>
      <maxWeakCount>90</maxWeakCount>
      <stageThreshold>-1.2794928550720215e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 568 -4.7569684684276581e-03</internalNodes>
          <leafValues>
            4.4339472055435181e-01 -1.0486443340778351e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 795 -2.5423073675483465e-03</internalNodes>
          <leafValues>
            3.9922216534614563e-01 -1.0431514680385590e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 649 1.1162508279085159e-02</internalNodes>
          <leafValues>
            -1.5686489641666412e-01 2.3129878938198090e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 847 1.7287035007029772e-03</internalNodes>
          <leafValues>
            -1.5123696625232697e-01 2.9676723480224609e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 265 2.5025676935911179e-02</internalNodes>
          <leafValues>
            -5.1661748439073563e-02 4.8509848117828369e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 78 1.2561861425638199e-02</internalNodes>
          <leafValues>
            -1.1817755550146103e-01 2.6937758922576904e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 812 4.6598571352660656e-03</internalNodes>
          <leafValues>
            -1.3565555214881897e-01 2.1206009387969971e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 434 7.4310216587036848e-04</internalNodes>
          <leafValues>
            -1.7020516097545624e-01 1.5990819036960602e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 231 1.0259399190545082e-02</internalNodes>
          <leafValues>
            -1.4796857535839081e-01 1.8798792362213135e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 278 -1.2777388095855713e-02</internalNodes>
          <leafValues>
            -5.4041445255279541e-01 4.8501875251531601e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 489 -1.1427352204918861e-02</internalNodes>
          <leafValues>
            -5.1071381568908691e-01 4.8088576644659042e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 819 2.8340169592411257e-05</internalNodes>
          <leafValues>
            -2.0961570739746094e-01 1.0582420229911804e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 325 -6.4714960753917694e-03</internalNodes>
          <leafValues>
            -5.0862830877304077e-01 4.8812258988618851e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 367 1.3540303334593773e-02</internalNodes>
          <leafValues>
            2.7134107425808907e-02 -7.1317195892333984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 1.8916794797405601e-03</internalNodes>
          <leafValues>
            -6.2187314033508301e-02 3.6233416199684143e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 51 1.0457850992679596e-02</internalNodes>
          <leafValues>
            4.0487006306648254e-02 -5.3173840045928955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 893 -9.0822251513600349e-04</internalNodes>
          <leafValues>
            2.0090451836585999e-01 -1.0807146877050400e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 535 -1.9299473613500595e-02</internalNodes>
          <leafValues>
            -6.4914399385452271e-01 4.0790289640426636e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 663 -8.2283990923315287e-04</internalNodes>
          <leafValues>
            1.5708251297473907e-01 -1.3143004477024078e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 523 3.7520762998610735e-03</internalNodes>
          <leafValues>
            3.8761712610721588e-02 -4.9775493144989014e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 762 8.2424264401197433e-03</internalNodes>
          <leafValues>
            3.6369498819112778e-02 -5.1153117418289185e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 805 -1.1945937294512987e-03</internalNodes>
          <leafValues>
            1.3862735033035278e-01 -1.3917639851570129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 985 -1.0589268989861012e-02</internalNodes>
          <leafValues>
            3.2981950044631958e-01 -7.6042778789997101e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 128 2.6780981570482254e-02</internalNodes>
          <leafValues>
            4.6954374760389328e-02 -4.5390221476554871e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 705 5.2458671852946281e-03</internalNodes>
          <leafValues>
            -4.7804936766624451e-02 4.0361502766609192e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 729 1.0518019553273916e-03</internalNodes>
          <leafValues>
            -1.0052871704101562e-01 1.9928459823131561e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 407 3.9210864342749119e-03</internalNodes>
          <leafValues>
            3.6381114274263382e-02 -5.4954099655151367e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 873 -1.5182888135313988e-02</internalNodes>
          <leafValues>
            2.8286656737327576e-01 -7.6106920838356018e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 279 2.7552489191293716e-03</internalNodes>
          <leafValues>
            -1.2027227133512497e-01 2.0814672112464905e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 869 1.3051946647465229e-02</internalNodes>
          <leafValues>
            3.6561664193868637e-02 -6.8296074867248535e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 849 4.4104140251874924e-03</internalNodes>
          <leafValues>
            2.9448021203279495e-02 -5.9994471073150635e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 799 2.3885946720838547e-03</internalNodes>
          <leafValues>
            3.9816807955503464e-02 -4.6116915345191956e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 551 2.3683100007474422e-03</internalNodes>
          <leafValues>
            4.9801617860794067e-02 -3.9546611905097961e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 707 -4.1178334504365921e-03</internalNodes>
          <leafValues>
            1.6903834044933319e-01 -1.1102814227342606e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 466 -2.7111368253827095e-03</internalNodes>
          <leafValues>
            2.0166625082492828e-01 -9.3054622411727905e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 360 -2.4442467838525772e-03</internalNodes>
          <leafValues>
            1.3419428467750549e-01 -1.4021472632884979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 104 -6.9398069754242897e-03</internalNodes>
          <leafValues>
            -4.7041961550712585e-01 3.8327444344758987e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 14 -7.5376339256763458e-02</internalNodes>
          <leafValues>
            3.5196593403816223e-01 -5.8293107897043228e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 270 -7.3061959119513631e-04</internalNodes>
          <leafValues>
            2.0563322305679321e-01 -9.7862586379051208e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 339 -4.4864090159535408e-03</internalNodes>
          <leafValues>
            -4.3219071626663208e-01 4.6815373003482819e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 679 -3.3369990997016430e-03</internalNodes>
          <leafValues>
            -5.7968968152999878e-01 3.2250367105007172e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 636 -5.7756435126066208e-03</internalNodes>
          <leafValues>
            -6.3823670148849487e-01 2.6716385036706924e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 352 3.8174313958734274e-03</internalNodes>
          <leafValues>
            -7.8204549849033356e-02 2.4104152619838715e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 414 3.9163082838058472e-03</internalNodes>
          <leafValues>
            4.0961768478155136e-02 -4.2656800150871277e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 670 -3.7615487817674875e-03</internalNodes>
          <leafValues>
            2.0846015214920044e-01 -8.6097449064254761e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 371 -9.5803234726190567e-03</internalNodes>
          <leafValues>
            -7.0837384462356567e-01 2.8397833928465843e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 93 1.4632595703005791e-02</internalNodes>
          <leafValues>
            1.8669826909899712e-02 -7.4236363172531128e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 234 5.3799869492650032e-03</internalNodes>
          <leafValues>
            3.0915707349777222e-02 -4.7074958682060242e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 701 -2.4318110663443804e-03</internalNodes>
          <leafValues>
            3.0304560065269470e-01 -5.6169599294662476e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 641 3.8594864308834076e-02</internalNodes>
          <leafValues>
            2.5472542271018028e-02 -6.8472218513488770e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 125 1.6673290729522705e-01</internalNodes>
          <leafValues>
            -5.9959251433610916e-02 2.9591250419616699e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 854 -5.0129964947700500e-03</internalNodes>
          <leafValues>
            1.9718486070632935e-01 -9.4902090728282928e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 960 -9.3115903437137604e-03</internalNodes>
          <leafValues>
            2.8306549787521362e-01 -6.8168632686138153e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 804 -2.7176579460501671e-03</internalNodes>
          <leafValues>
            2.4883794784545898e-01 -7.3830418288707733e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 787 6.9358374457806349e-04</internalNodes>
          <leafValues>
            -1.2474948167800903e-01 1.6316886246204376e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 783 1.3523821253329515e-03</internalNodes>
          <leafValues>
            -7.3475763201713562e-02 3.0120497941970825e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 532 -2.6339504867792130e-02</internalNodes>
          <leafValues>
            4.7823980450630188e-01 -3.9222836494445801e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 866 3.3510509878396988e-02</internalNodes>
          <leafValues>
            -3.8013227283954620e-02 4.1955846548080444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 694 -2.8097369067836553e-05</internalNodes>
          <leafValues>
            1.2249568104743958e-01 -1.4184975624084473e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 988 -4.0141213685274124e-03</internalNodes>
          <leafValues>
            -4.5551317930221558e-01 3.6903131753206253e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 934 5.7984986342489719e-03</internalNodes>
          <leafValues>
            3.9383981376886368e-02 -4.0305584669113159e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 753 7.5392555445432663e-03</internalNodes>
          <leafValues>
            -9.3996182084083557e-02 1.8520636856555939e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 943 4.5007485896348953e-03</internalNodes>
          <leafValues>
            4.2565450072288513e-02 -4.0628531575202942e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 500 5.0333794206380844e-03</internalNodes>
          <leafValues>
            -6.7051678895950317e-02 2.5224363803863525e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 511 8.7359821191057563e-04</internalNodes>
          <leafValues>
            -9.5469102263450623e-02 1.7292767763137817e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 771 3.0778967775404453e-03</internalNodes>
          <leafValues>
            -6.1908006668090820e-02 2.5266119837760925e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 835 -2.2874618880450726e-03</internalNodes>
          <leafValues>
            1.9187310338020325e-01 -8.5145145654678345e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 634 4.0947222150862217e-03</internalNodes>
          <leafValues>
            3.0908439308404922e-02 -5.5290663242340088e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 488 2.1358881145715714e-02</internalNodes>
          <leafValues>
            4.0033571422100067e-02 -3.8174301385879517e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 159 -4.5840246602892876e-03</internalNodes>
          <leafValues>
            -5.2027910947799683e-01 3.0034648254513741e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 232 9.8655056208372116e-03</internalNodes>
          <leafValues>
            2.1588459610939026e-02 -6.3089925050735474e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 223 2.5678081437945366e-03</internalNodes>
          <leafValues>
            -1.1046713590621948e-01 1.4713281393051147e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 688 -2.6078277733176947e-03</internalNodes>
          <leafValues>
            2.7103677392005920e-01 -5.9257075190544128e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 355 2.6908484287559986e-03</internalNodes>
          <leafValues>
            2.7514556422829628e-02 -6.3733005523681641e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 715 -1.3983637327328324e-03</internalNodes>
          <leafValues>
            1.5699537098407745e-01 -1.0462216287851334e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 433 1.0498151183128357e-01</internalNodes>
          <leafValues>
            3.0471364036202431e-02 -4.9990084767341614e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 491 -1.4592260122299194e-01</internalNodes>
          <leafValues>
            3.2007977366447449e-01 -5.2097231149673462e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 825 7.8754723072052002e-03</internalNodes>
          <leafValues>
            -6.7778728902339935e-02 2.8044930100440979e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 262 -5.3792521357536316e-03</internalNodes>
          <leafValues>
            2.1354769170284271e-01 -8.2902953028678894e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 420 -1.0021779686212540e-02</internalNodes>
          <leafValues>
            2.5685080885887146e-01 -7.3165819048881531e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 1 -4.2762188240885735e-03</internalNodes>
          <leafValues>
            1.7162682116031647e-01 -9.7696490585803986e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 67 1.0965526103973389e-02</internalNodes>
          <leafValues>
            -7.5053967535495758e-02 2.3615135252475739e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 328 -4.4276113621890545e-03</internalNodes>
          <leafValues>
            2.5747051835060120e-01 -6.3898853957653046e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 276 -8.6840223520994186e-03</internalNodes>
          <leafValues>
            -4.7478455305099487e-01 3.6790292710065842e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 938 2.8339526616036892e-03</internalNodes>
          <leafValues>
            4.0944386273622513e-02 -3.6514538526535034e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 790 7.6391562819480896e-02</internalNodes>
          <leafValues>
            -4.9489263445138931e-02 3.4142583608627319e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 1.9103729864582419e-03</internalNodes>
          <leafValues>
            -5.6329321116209030e-02 2.9177185893058777e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 304 5.2499733865261078e-02</internalNodes>
          <leafValues>
            2.8848636895418167e-02 -5.9306102991104126e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 956 -5.0793914124369621e-03</internalNodes>
          <leafValues>
            -5.0588577985763550e-01 2.8303196653723717e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 967 -7.1491668932139874e-03</internalNodes>
          <leafValues>
            -6.2660187482833862e-01 2.3113224655389786e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 17 -->
    <_>
      <maxWeakCount>88</maxWeakCount>
      <stageThreshold>-1.2153301239013672e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 803 3.5730558447539806e-03</internalNodes>
          <leafValues>
            -4.2218949645757675e-02 5.5067819356918335e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 520 1.0531613603234291e-02</internalNodes>
          <leafValues>
            -1.0848262906074524e-01 4.2079353332519531e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 570 -2.8240748215466738e-03</internalNodes>
          <leafValues>
            1.5155430138111115e-01 -2.2742147743701935e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 384 -1.6008135862648487e-03</internalNodes>
          <leafValues>
            2.9879093170166016e-01 -1.0573560744524002e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 90 -1.2082614004611969e-02</internalNodes>
          <leafValues>
            2.5803449749946594e-01 -1.1197961121797562e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 746 9.8490377422422171e-04</internalNodes>
          <leafValues>
            -1.8312133848667145e-01 1.3942104578018188e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 347 1.3184763491153717e-02</internalNodes>
          <leafValues>
            -1.0306112468242645e-01 2.5403776764869690e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 143 2.5388993322849274e-02</internalNodes>
          <leafValues>
            6.4101323485374451e-02 -4.2444714903831482e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 196 7.8083951957523823e-03</internalNodes>
          <leafValues>
            -7.8133262693881989e-02 3.2170715928077698e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 921 1.2125947978347540e-03</internalNodes>
          <leafValues>
            -1.4831624925136566e-01 1.6055701673030853e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 920 -5.7722916826605797e-03</internalNodes>
          <leafValues>
            -6.2254351377487183e-01 4.7926213592290878e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 987 -6.7740413360297680e-03</internalNodes>
          <leafValues>
            -6.4991837739944458e-01 1.9058052450418472e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 291 -2.8847754001617432e-03</internalNodes>
          <leafValues>
            -5.1574712991714478e-01 4.2939033359289169e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 922 -5.1092512905597687e-02</internalNodes>
          <leafValues>
            -7.1794927120208740e-01 3.0500946566462517e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 303 -3.0863287393003702e-03</internalNodes>
          <leafValues>
            -5.1027435064315796e-01 3.7360988557338715e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 593 -3.1833123648539186e-04</internalNodes>
          <leafValues>
            1.1626140773296356e-01 -1.7245446145534515e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 1.2636608444154263e-03</internalNodes>
          <leafValues>
            -7.4942886829376221e-02 2.7081242203712463e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 693 -2.7436314150691032e-02</internalNodes>
          <leafValues>
            -5.7718968391418457e-01 3.3168055117130280e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 342 -1.8837231909856200e-03</internalNodes>
          <leafValues>
            -3.0960574746131897e-01 6.1044581234455109e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 797 3.2289433293044567e-03</internalNodes>
          <leafValues>
            -6.8203814327716827e-02 2.9658797383308411e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 503 -3.6236688029021025e-03</internalNodes>
          <leafValues>
            -4.9605649709701538e-01 4.2492914944887161e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 135 -1.3776571722701192e-03</internalNodes>
          <leafValues>
            1.3447758555412292e-01 -1.3678476214408875e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 579 2.9051192104816437e-03</internalNodes>
          <leafValues>
            -1.2944447994232178e-01 1.4306847751140594e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 722 4.4553354382514954e-03</internalNodes>
          <leafValues>
            3.8421813398599625e-02 -4.5035859942436218e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 622 1.0964765213429928e-02</internalNodes>
          <leafValues>
            -4.8769049346446991e-02 3.9813303947448730e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 682 2.8863823972642422e-03</internalNodes>
          <leafValues>
            5.1313977688550949e-02 -3.6272794008255005e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 283 8.8652484118938446e-03</internalNodes>
          <leafValues>
            -9.4886533915996552e-02 2.1068450808525085e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 333 -1.9646657630801201e-02</internalNodes>
          <leafValues>
            2.2927023470401764e-01 -1.0384474694728851e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 684 -2.3328745737671852e-03</internalNodes>
          <leafValues>
            -3.0931735038757324e-01 6.4516365528106689e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 8 -4.0204055607318878e-02</internalNodes>
          <leafValues>
            2.7381995320320129e-01 -7.6448827981948853e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 100 1.9051276147365570e-02</internalNodes>
          <leafValues>
            4.9466736614704132e-02 -3.6089882254600525e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 936 1.1553505435585976e-02</internalNodes>
          <leafValues>
            -7.4454858899116516e-02 2.5223839282989502e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 76 6.0810474678874016e-03</internalNodes>
          <leafValues>
            4.9583721905946732e-02 -3.6660569906234741e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 212 5.4147411137819290e-03</internalNodes>
          <leafValues>
            3.2274514436721802e-02 -4.9895319342613220e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 544 4.6544210053980350e-03</internalNodes>
          <leafValues>
            2.5989409536123276e-02 -6.1053085327148438e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 166 2.4446439929306507e-03</internalNodes>
          <leafValues>
            -1.2073440849781036e-01 1.4529803395271301e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 698 4.6318914974108338e-04</internalNodes>
          <leafValues>
            -1.0553400218486786e-01 1.7337696254253387e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 642 -3.7485856562852859e-02</internalNodes>
          <leafValues>
            -4.0581890940666199e-01 4.1759915649890900e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 529 -2.0438145846128464e-02</internalNodes>
          <leafValues>
            2.9171264171600342e-01 -6.6287793219089508e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 524 -3.8345486391335726e-03</internalNodes>
          <leafValues>
            1.5750087797641754e-01 -1.2569475173950195e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 884 8.8059913832694292e-04</internalNodes>
          <leafValues>
            -1.0610871762037277e-01 1.7642241716384888e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 33 2.0514219067990780e-03</internalNodes>
          <leafValues>
            3.4303460270166397e-02 -5.5235451459884644e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 -3.5282317548990250e-03</internalNodes>
          <leafValues>
            -5.3414058685302734e-01 3.0512372031807899e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 506 6.1051873490214348e-03</internalNodes>
          <leafValues>
            -8.4812760353088379e-02 1.9969700276851654e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 -6.4141638576984406e-03</internalNodes>
          <leafValues>
            -4.0772309899330139e-01 4.3864764273166656e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 823 1.7272554337978363e-02</internalNodes>
          <leafValues>
            2.1965105086565018e-02 -6.9809681177139282e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 512 -1.9691141787916422e-03</internalNodes>
          <leafValues>
            1.8511210381984711e-01 -9.0554594993591309e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 59 -5.5513512343168259e-03</internalNodes>
          <leafValues>
            -4.2040807008743286e-01 4.0062893182039261e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 626 -1.1905157566070557e-01</internalNodes>
          <leafValues>
            -6.4312189817428589e-01 2.3472266271710396e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 290 4.0823101997375488e-02</internalNodes>
          <leafValues>
            -7.3068141937255859e-02 2.4851579964160919e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 119 -8.1011475995182991e-03</internalNodes>
          <leafValues>
            2.2747313976287842e-01 -7.5412914156913757e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 87 4.7750310041010380e-03</internalNodes>
          <leafValues>
            -7.8901365399360657e-02 2.3182301223278046e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 404 -2.7586806565523148e-02</internalNodes>
          <leafValues>
            -6.4926701784133911e-01 2.5375340133905411e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 907 4.3069543316960335e-03</internalNodes>
          <leafValues>
            2.4360222741961479e-02 -5.7372909784317017e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 385 -6.1931653181090951e-04</internalNodes>
          <leafValues>
            2.2557340562343597e-01 -7.5787223875522614e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 50 -1.1459679901599884e-01</internalNodes>
          <leafValues>
            3.0668416619300842e-01 -5.2840072661638260e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 239 3.1560026109218597e-02</internalNodes>
          <leafValues>
            -9.5666781067848206e-02 1.7659574747085571e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 871 1.5142546035349369e-03</internalNodes>
          <leafValues>
            -9.2694908380508423e-02 2.0833927392959595e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 731 4.7312509268522263e-03</internalNodes>
          <leafValues>
            -4.9851816147565842e-02 3.4422698616981506e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 253 -5.9051956050097942e-03</internalNodes>
          <leafValues>
            -4.6798244118690491e-01 3.6009732633829117e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 703 3.3569703809916973e-03</internalNodes>
          <leafValues>
            -5.1445800811052322e-02 3.3950069546699524e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 966 -1.1821147799491882e-01</internalNodes>
          <leafValues>
            4.6877983212471008e-01 -3.2708466053009033e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 363 -8.8651233818382025e-04</internalNodes>
          <leafValues>
            1.5177871286869049e-01 -1.0880727320909500e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 680 -2.5330238044261932e-02</internalNodes>
          <leafValues>
            1.7184022068977356e-01 -9.8979160189628601e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 770 5.5901473388075829e-03</internalNodes>
          <leafValues>
            -7.1004293859004974e-02 2.7359166741371155e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 189 1.2344302609562874e-02</internalNodes>
          <leafValues>
            3.2738436013460159e-02 -5.2876019477844238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 348 -7.4871592223644257e-03</internalNodes>
          <leafValues>
            -5.1955360174179077e-01 2.7597136795520782e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 646 -2.6753707788884640e-03</internalNodes>
          <leafValues>
            -4.7180628776550293e-01 3.1411368399858475e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 168 -3.2419776543974876e-03</internalNodes>
          <leafValues>
            1.5980260074138641e-01 -9.5776490867137909e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 169 8.8083129376173019e-03</internalNodes>
          <leafValues>
            -8.2104682922363281e-02 2.0850872993469238e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 58 2.7282098308205605e-03</internalNodes>
          <leafValues>
            6.1908718198537827e-02 -2.6338595151901245e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 671 5.0587565638124943e-03</internalNodes>
          <leafValues>
            -8.2083821296691895e-02 1.9557759165763855e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 708 -2.1199107170104980e-02</internalNodes>
          <leafValues>
            -5.0425887107849121e-01 3.0914928764104843e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 723 3.4958114847540855e-03</internalNodes>
          <leafValues>
            -8.2294017076492310e-02 1.9164223968982697e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 842 1.5914414543658495e-03</internalNodes>
          <leafValues>
            -6.9352962076663971e-02 2.1474194526672363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 193 -5.0045788288116455e-02</internalNodes>
          <leafValues>
            2.4582423269748688e-01 -6.2959901988506317e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 19 -4.1983526200056076e-02</internalNodes>
          <leafValues>
            -6.3210010528564453e-01 2.5985429063439369e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 402 -6.9432961754500866e-04</internalNodes>
          <leafValues>
            2.2444137930870056e-01 -7.0591680705547333e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 540 6.0177911072969437e-03</internalNodes>
          <leafValues>
            3.7622205913066864e-02 -4.1375440359115601e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 492 4.7936867922544479e-03</internalNodes>
          <leafValues>
            -9.0203136205673218e-02 1.7498855292797089e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 390 -4.7484524548053741e-03</internalNodes>
          <leafValues>
            -3.9998278021812439e-01 3.8966752588748932e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 620 -7.7324017882347107e-02</internalNodes>
          <leafValues>
            -4.8634868860244751e-01 2.9687402769923210e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 417 1.1184449307620525e-02</internalNodes>
          <leafValues>
            -4.9598570913076401e-02 3.2780852913856506e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 132 -1.0921864770352840e-02</internalNodes>
          <leafValues>
            1.7756749689579010e-01 -8.5219532251358032e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 357 4.5135535299777985e-02</internalNodes>
          <leafValues>
            2.8995228931307793e-02 -5.3758519887924194e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 341 -1.1866749264299870e-03</internalNodes>
          <leafValues>
            1.8304300308227539e-01 -8.5605643689632416e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 609 2.0626676268875599e-03</internalNodes>
          <leafValues>
            2.5438303127884865e-02 -5.9883767366409302e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 251 2.7453177608549595e-05</internalNodes>
          <leafValues>
            -1.3831512629985809e-01 1.0590004175901413e-01</leafValues></_></weakClassifiers></_>
    <!-- stage 18 -->
    <_>
      <maxWeakCount>98</maxWeakCount>
      <stageThreshold>-1.2823635339736938e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 840 -8.7535101920366287e-03</internalNodes>
          <leafValues>
            3.7845414876937866e-01 -1.2724789977073669e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 376 -5.7867290452122688e-03</internalNodes>
          <leafValues>
            4.6451708674430847e-01 -1.0028645396232605e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 467 -1.5636831521987915e-02</internalNodes>
          <leafValues>
            2.7137696743011475e-01 -1.3237486779689789e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 743 7.9419813118875027e-04</internalNodes>
          <leafValues>
            -2.2457434237003326e-01 1.8765783309936523e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 511 9.8101666662842035e-04</internalNodes>
          <leafValues>
            -1.1674020439386368e-01 2.3788549005985260e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 148 -1.1779682245105505e-03</internalNodes>
          <leafValues>
            2.5913080573081970e-01 -8.3949849009513855e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 330 9.6748135983943939e-03</internalNodes>
          <leafValues>
            -8.3296068012714386e-02 3.4700453281402588e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 307 2.9431451112031937e-03</internalNodes>
          <leafValues>
            4.6826824545860291e-02 -5.1865130662918091e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 918 -1.0496248723939061e-03</internalNodes>
          <leafValues>
            -2.9976195096969604e-01 6.9594070315361023e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 697 -1.6385620459914207e-02</internalNodes>
          <leafValues>
            2.1480703353881836e-01 -9.7807772457599640e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 910 4.9830954521894455e-03</internalNodes>
          <leafValues>
            2.2837642580270767e-02 -7.7743059396743774e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 796 -3.1421617604792118e-03</internalNodes>
          <leafValues>
            -5.6898134946823120e-01 3.6988433450460434e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 901 1.6069117933511734e-02</internalNodes>
          <leafValues>
            -1.0548119246959686e-01 1.9650301337242126e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 751 1.5043821185827255e-02</internalNodes>
          <leafValues>
            -1.0749972611665726e-01 2.0178599655628204e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 295 6.8444460630416870e-03</internalNodes>
          <leafValues>
            5.0306834280490875e-02 -4.3162798881530762e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 827 1.1850953102111816e-02</internalNodes>
          <leafValues>
            3.2905589789152145e-02 -5.1617246866226196e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 831 2.1246306598186493e-02</internalNodes>
          <leafValues>
            -6.3726536929607391e-02 3.0544599890708923e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 256 1.1852337047457695e-02</internalNodes>
          <leafValues>
            -8.9553833007812500e-02 2.9359081387519836e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 323 -2.5085010565817356e-03</internalNodes>
          <leafValues>
            2.2805334627628326e-01 -9.5263637602329254e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 752 7.5797801837325096e-03</internalNodes>
          <leafValues>
            3.8756053894758224e-02 -5.7552194595336914e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 86 5.4980744607746601e-03</internalNodes>
          <leafValues>
            4.6144284307956696e-02 -3.6506399512290955e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 208 -3.0190458055585623e-03</internalNodes>
          <leafValues>
            -2.9709556698799133e-01 7.5851216912269592e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 552 -7.0441095158457756e-03</internalNodes>
          <leafValues>
            1.6086654365062714e-01 -1.1914677917957306e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 364 -6.9178184494376183e-03</internalNodes>
          <leafValues>
            -4.1069602966308594e-01 4.4916272163391113e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 351 5.0740875303745270e-03</internalNodes>
          <leafValues>
            -7.4677795171737671e-02 2.4945564568042755e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 121 -1.0403880849480629e-02</internalNodes>
          <leafValues>
            -5.3336864709854126e-01 3.9480298757553101e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 323 2.3738082963973284e-03</internalNodes>
          <leafValues>
            -7.8084513545036316e-02 2.3774850368499756e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 391 2.7033074729843065e-05</internalNodes>
          <leafValues>
            -1.8558554351329803e-01 9.6640095114707947e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 167 2.9049259610474110e-03</internalNodes>
          <leafValues>
            4.6409133821725845e-02 -3.9720407128334045e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 181 -5.6298477575182915e-03</internalNodes>
          <leafValues>
            -4.5908093452453613e-01 3.7730857729911804e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 638 5.0751655362546444e-03</internalNodes>
          <leafValues>
            2.3507807403802872e-02 -6.4602053165435791e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 909 -7.5826002284884453e-04</internalNodes>
          <leafValues>
            1.2444372475147247e-01 -1.3639765977859497e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 11 -9.7201213240623474e-02</internalNodes>
          <leafValues>
            3.9986947178840637e-01 -4.4366274029016495e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 496 -2.3840454220771790e-01</internalNodes>
          <leafValues>
            -5.3094118833541870e-01 3.8410611450672150e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 114 -1.3428549282252789e-02</internalNodes>
          <leafValues>
            2.2794343531131744e-01 -7.7827021479606628e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 64 -5.0623202696442604e-04</internalNodes>
          <leafValues>
            1.5778008103370667e-01 -1.2732668220996857e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 931 -8.6578715126961470e-04</internalNodes>
          <leafValues>
            1.4809772372245789e-01 -1.1785575747489929e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 544 -2.7892580255866051e-03</internalNodes>
          <leafValues>
            -4.2324438691139221e-01 4.1194166988134384e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 654 2.9110969044268131e-03</internalNodes>
          <leafValues>
            -1.2145258486270905e-01 1.4758351445198059e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 122 -1.7908504605293274e-01</internalNodes>
          <leafValues>
            4.0684828162193298e-01 -4.6298943459987640e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 894 4.2685694643296301e-04</internalNodes>
          <leafValues>
            -9.4548642635345459e-02 1.8615303933620453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 72 1.9871112704277039e-01</internalNodes>
          <leafValues>
            -5.6818448007106781e-02 3.2197028398513794e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 892 1.2496551498770714e-03</internalNodes>
          <leafValues>
            -7.0664338767528534e-02 2.5729593634605408e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 447 1.6119793057441711e-02</internalNodes>
          <leafValues>
            -5.0713617354631424e-02 3.9684635400772095e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 964 -2.5047704111784697e-03</internalNodes>
          <leafValues>
            -3.5733562707901001e-01 4.9460943788290024e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 672 5.2866833284497261e-03</internalNodes>
          <leafValues>
            3.2510578632354736e-02 -4.4326359033584595e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 633 -3.4677600488066673e-03</internalNodes>
          <leafValues>
            2.3254001140594482e-01 -7.3516972362995148e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 600 -3.3557973802089691e-03</internalNodes>
          <leafValues>
            2.3221854865550995e-01 -6.9719336926937103e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 801 -6.3276281580328941e-03</internalNodes>
          <leafValues>
            -4.0112924575805664e-01 4.3525256216526031e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 218 -4.3456726707518101e-03</internalNodes>
          <leafValues>
            -6.8020933866500854e-01 1.9806224852800369e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 604 6.2400596216320992e-03</internalNodes>
          <leafValues>
            1.8352568149566650e-02 -7.0223194360733032e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 979 3.3795731142163277e-03</internalNodes>
          <leafValues>
            4.3487045913934708e-02 -3.0831974744796753e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 937 1.3499217107892036e-02</internalNodes>
          <leafValues>
            -4.4923197478055954e-02 3.2624542713165283e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 408 -1.0585743002593517e-03</internalNodes>
          <leafValues>
            1.6033367812633514e-01 -9.8465800285339355e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 405 -5.3765797056257725e-03</internalNodes>
          <leafValues>
            2.6544988155364990e-01 -6.7050188779830933e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 980 -2.4880110286176205e-03</internalNodes>
          <leafValues>
            -2.9397118091583252e-01 5.4097402840852737e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 505 -2.1792344748973846e-02</internalNodes>
          <leafValues>
            -7.2506862878799438e-01 1.9187789410352707e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 714 4.7056311741471291e-03</internalNodes>
          <leafValues>
            -5.2215453237295151e-02 3.1615570187568665e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 669 -4.2645912617444992e-03</internalNodes>
          <leafValues>
            2.3567616939544678e-01 -6.8938009440898895e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 774 5.8556320145726204e-03</internalNodes>
          <leafValues>
            4.2000979185104370e-02 -4.6045160293579102e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 926 1.3632343616336584e-03</internalNodes>
          <leafValues>
            -6.5663956105709076e-02 2.3397234082221985e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 895 -6.0495175421237946e-03</internalNodes>
          <leafValues>
            -4.3943586945533752e-01 3.6742802709341049e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 308 6.7223357036709785e-03</internalNodes>
          <leafValues>
            1.9922675564885139e-02 -6.8767511844635010e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 917 -5.1960002630949020e-02</internalNodes>
          <leafValues>
            -7.5993520021438599e-01 1.5627101063728333e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 542 3.3762669190764427e-03</internalNodes>
          <leafValues>
            -7.7943108975887299e-02 1.9545321166515350e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 582 -1.8302195239812136e-03</internalNodes>
          <leafValues>
            1.9154363870620728e-01 -9.4946600496768951e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 71 -4.3824277818202972e-03</internalNodes>
          <leafValues>
            -5.3172159194946289e-01 2.8438575565814972e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 107 4.8605538904666901e-03</internalNodes>
          <leafValues>
            1.8084224313497543e-02 -7.0419138669967651e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 289 -5.0755832344293594e-03</internalNodes>
          <leafValues>
            1.3961549103260040e-01 -1.0557857155799866e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 349 9.0303886681795120e-03</internalNodes>
          <leafValues>
            -5.6681722402572632e-02 3.0537691712379456e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 52 1.7635107040405273e-01</internalNodes>
          <leafValues>
            -3.5581633448600769e-02 3.9358299970626831e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 728 1.1068049352616072e-03</internalNodes>
          <leafValues>
            -9.6729792654514313e-02 1.6677951812744141e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 162 1.1059102602303028e-02</internalNodes>
          <leafValues>
            2.9283966869115829e-02 -5.1121145486831665e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 236 -5.0462923943996429e-02</internalNodes>
          <leafValues>
            -4.2722624540328979e-01 3.1082244589924812e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 316 -3.8071773014962673e-03</internalNodes>
          <leafValues>
            2.9747742414474487e-01 -5.1289469003677368e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 373 -1.5183673240244389e-03</internalNodes>
          <leafValues>
            1.8215130269527435e-01 -1.0301912575960159e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 258 2.1069757640361786e-02</internalNodes>
          <leafValues>
            2.4503789842128754e-02 -5.8991265296936035e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 68 6.6435593180358410e-03</internalNodes>
          <leafValues>
            4.3313629925251007e-02 -3.1504327058792114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 574 -8.2504414021968842e-03</internalNodes>
          <leafValues>
            -4.7998124361038208e-01 3.0433293431997299e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 617 -1.0892231017351151e-02</internalNodes>
          <leafValues>
            3.1449675559997559e-01 -5.2475348114967346e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 213 8.1554818898439407e-03</internalNodes>
          <leafValues>
            3.9224579930305481e-02 -3.8470247387886047e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 838 -5.4475883953273296e-03</internalNodes>
          <leafValues>
            -6.5578418970108032e-01 2.0117431879043579e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 487 -2.6005427935160697e-04</internalNodes>
          <leafValues>
            1.4328984916210175e-01 -9.8999619483947754e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 461 1.3821206521242857e-03</internalNodes>
          <leafValues>
            -5.2590593695640564e-02 2.7557003498077393e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 445 -1.1740636080503464e-02</internalNodes>
          <leafValues>
            2.7564841508865356e-01 -5.9799015522003174e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 941 2.7866149321198463e-03</internalNodes>
          <leafValues>
            5.0002526491880417e-02 -3.5232934355735779e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 962 6.6179647110402584e-03</internalNodes>
          <leafValues>
            -6.3348092138767242e-02 2.3150660097599030e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 297 -1.3244405854493380e-03</internalNodes>
          <leafValues>
            -2.6642721891403198e-01 5.5936500430107117e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 485 1.1830568313598633e-02</internalNodes>
          <leafValues>
            -6.9061063230037689e-02 2.1172530949115753e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 2.5925931986421347e-03</internalNodes>
          <leafValues>
            1.9716180860996246e-02 -7.7208590507507324e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 748 -2.8010653331875801e-03</internalNodes>
          <leafValues>
            1.3846111297607422e-01 -9.7015053033828735e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 144 -4.7637272626161575e-02</internalNodes>
          <leafValues>
            2.1245625615119934e-01 -7.0445045828819275e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 197 1.3677144888788462e-03</internalNodes>
          <leafValues>
            -8.5676178336143494e-02 1.9613882899284363e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 556 -1.3261453807353973e-01</internalNodes>
          <leafValues>
            4.3639957904815674e-01 -3.4653130918741226e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 69 7.1225965023040771e-01</internalNodes>
          <leafValues>
            1.9474601373076439e-02 -8.7232232093811035e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 149 -5.9057516045868397e-03</internalNodes>
          <leafValues>
            -3.7135502696037292e-01 3.5206548869609833e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 971 3.5532126203179359e-03</internalNodes>
          <leafValues>
            -6.6334858536720276e-02 2.3531165719032288e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 31 -1.9724387675523758e-02</internalNodes>
          <leafValues>
            2.5173032283782959e-01 -5.7575348764657974e-02</leafValues></_></weakClassifiers></_>
    <!-- stage 19 -->
    <_>
      <maxWeakCount>100</maxWeakCount>
      <stageThreshold>-1.3067311048507690e+00</stageThreshold>
      <weakClassifiers>
        <_>
          <internalNodes>
            0 -1 458 8.1832958385348320e-03</internalNodes>
          <leafValues>
            -1.1180391162633896e-01 3.9526882767677307e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 717 -5.5650249123573303e-03</internalNodes>
          <leafValues>
            3.3437621593475342e-01 -1.2654128670692444e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 577 8.1406952813267708e-04</internalNodes>
          <leafValues>
            -1.7086146771907806e-01 1.8384252488613129e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 113 -2.0645279437303543e-03</internalNodes>
          <leafValues>
            1.7057111859321594e-01 -1.7103828489780426e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 864 1.9037863239645958e-03</internalNodes>
          <leafValues>
            -1.6791534423828125e-01 1.5749432146549225e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 242 1.1136581189930439e-02</internalNodes>
          <leafValues>
            4.0173061192035675e-02 -3.7364640831947327e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 228 5.6379067245870829e-04</internalNodes>
          <leafValues>
            -1.6792711615562439e-01 1.4207355678081512e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 797 -3.3720356877893209e-03</internalNodes>
          <leafValues>
            2.5698736310005188e-01 -7.5178287923336029e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 710 -1.7311582341790199e-02</internalNodes>
          <leafValues>
            -5.2065086364746094e-01 4.7350786626338959e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 845 -3.3407085575163364e-03</internalNodes>
          <leafValues>
            -4.5184752345085144e-01 3.2597322016954422e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 661 -3.4317255020141602e-02</internalNodes>
          <leafValues>
            2.5700893998146057e-01 -8.3455510437488556e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 423 -6.8267658352851868e-02</internalNodes>
          <leafValues>
            2.8288829326629639e-01 -7.8631594777107239e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 951 2.8722581191686913e-05</internalNodes>
          <leafValues>
            -1.8466357886791229e-01 1.1576397716999054e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 267 9.9579263478517532e-03</internalNodes>
          <leafValues>
            -6.3400641083717346e-02 3.6796927452087402e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 733 -1.8424488604068756e-02</internalNodes>
          <leafValues>
            2.4584248661994934e-01 -9.4283707439899445e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 837 6.8876314908266068e-03</internalNodes>
          <leafValues>
            -9.9725127220153809e-02 2.8111982345581055e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 657 -2.2637452930212021e-03</internalNodes>
          <leafValues>
            -4.1033151745796204e-01 6.1188895255327225e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 191 -8.5531552031170577e-05</internalNodes>
          <leafValues>
            1.1543370783329010e-01 -1.6276736557483673e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 32 3.3203132450580597e-02</internalNodes>
          <leafValues>
            4.8811107873916626e-02 -3.7535405158996582e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 929 5.1993243396282196e-03</internalNodes>
          <leafValues>
            3.9811953902244568e-02 -4.8758861422538757e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 365 4.8818998038768768e-03</internalNodes>
          <leafValues>
            2.4118293076753616e-02 -6.7809182405471802e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 82 -7.2956003248691559e-02</internalNodes>
          <leafValues>
            1.8825025856494904e-01 -9.5193333923816681e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 836 9.4123989343643188e-02</internalNodes>
          <leafValues>
            -7.2761356830596924e-02 2.7999758720397949e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 718 1.0472428984940052e-03</internalNodes>
          <leafValues>
            -7.4624419212341309e-02 2.4220877885818481e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 446 8.0979522317647934e-03</internalNodes>
          <leafValues>
            -5.4950036108493805e-02 3.0833497643470764e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 463 -2.8517602477222681e-03</internalNodes>
          <leafValues>
            3.2442548871040344e-01 -7.1306072175502777e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 63 3.7457090802490711e-03</internalNodes>
          <leafValues>
            5.7812750339508057e-02 -3.3119776844978333e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 217 -3.9520347490906715e-03</internalNodes>
          <leafValues>
            -4.3750977516174316e-01 3.9293695241212845e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 865 -5.8175362646579742e-03</internalNodes>
          <leafValues>
            2.0937338471412659e-01 -8.1724949181079865e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 878 7.8594256192445755e-03</internalNodes>
          <leafValues>
            4.8747915774583817e-02 -4.1596582531929016e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 913 -6.7130924435332417e-04</internalNodes>
          <leafValues>
            1.4715777337551117e-01 -1.2916122376918793e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 62 -4.2964564636349678e-03</internalNodes>
          <leafValues>
            -3.5870963335037231e-01 4.8831127583980560e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 868 -3.8814521394670010e-03</internalNodes>
          <leafValues>
            -4.7464737296104431e-01 3.4466378390789032e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 950 -1.8017216352745891e-03</internalNodes>
          <leafValues>
            -3.5517925024032593e-01 4.9101348966360092e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 813 7.7566690742969513e-03</internalNodes>
          <leafValues>
            2.7035165578126907e-02 -5.5951416492462158e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 1.9125882536172867e-03</internalNodes>
          <leafValues>
            -6.3309118151664734e-02 2.5223699212074280e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 886 -9.9804997444152832e-04</internalNodes>
          <leafValues>
            2.4349449574947357e-01 -8.9007876813411713e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 97 -7.5093598570674658e-04</internalNodes>
          <leafValues>
            1.3702079653739929e-01 -1.2293258309364319e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 7 1.0788314975798130e-02</internalNodes>
          <leafValues>
            -7.3592424392700195e-02 2.3694764077663422e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 428 -1.2814668007194996e-03</internalNodes>
          <leafValues>
            1.7014959454536438e-01 -9.3263216316699982e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 851 3.5997035447508097e-03</internalNodes>
          <leafValues>
            2.4880735203623772e-02 -5.7666695117950439e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 410 5.9913634322583675e-03</internalNodes>
          <leafValues>
            -6.6571407020092010e-02 2.3750782012939453e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 299 3.7381309084594250e-03</internalNodes>
          <leafValues>
            3.7266705185174942e-02 -4.3619966506958008e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 372 8.8815446943044662e-03</internalNodes>
          <leafValues>
            3.0544634908437729e-02 -4.6924960613250732e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 243 -3.1860180199146271e-02</internalNodes>
          <leafValues>
            -4.8059463500976562e-01 3.1165035441517830e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 881 -5.4914336651563644e-03</internalNodes>
          <leafValues>
            1.7584608495235443e-01 -9.0091012418270111e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 821 -1.2325609102845192e-02</internalNodes>
          <leafValues>
            3.4678825736045837e-01 -5.6969922035932541e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 281 5.8694169856607914e-03</internalNodes>
          <leafValues>
            3.9381653070449829e-02 -4.6237498521804810e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 207 -5.0925426185131073e-03</internalNodes>
          <leafValues>
            -4.0191245079040527e-01 4.1170045733451843e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 636 4.5132841914892197e-03</internalNodes>
          <leafValues>
            2.7933681383728981e-02 -4.8419687151908875e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 665 2.2130757570266724e-02</internalNodes>
          <leafValues>
            2.1358741447329521e-02 -6.0434627532958984e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 597 -1.8624030053615570e-03</internalNodes>
          <leafValues>
            1.9556084275245667e-01 -7.8905813395977020e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 599 3.2466566190123558e-03</internalNodes>
          <leafValues>
            -8.3141714334487915e-02 2.5859814882278442e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 575 1.9641252234578133e-02</internalNodes>
          <leafValues>
            2.1901637315750122e-02 -7.2247391939163208e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 271 1.2722628191113472e-02</internalNodes>
          <leafValues>
            -4.9173772335052490e-02 3.1656193733215332e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 210 -3.9457585080526769e-04</internalNodes>
          <leafValues>
            1.7969387769699097e-01 -1.0087045282125473e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 88 -3.0111533123999834e-04</internalNodes>
          <leafValues>
            1.2916654348373413e-01 -1.5019074082374573e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 84 -4.1901473887264729e-03</internalNodes>
          <leafValues>
            1.6727919876575470e-01 -9.4101771712303162e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 186 -2.9096096754074097e-02</internalNodes>
          <leafValues>
            2.4397623538970947e-01 -6.5033406019210815e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 815 -3.0687432736158371e-02</internalNodes>
          <leafValues>
            -5.3695982694625854e-01 3.6870311945676804e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 596 8.9634142816066742e-02</internalNodes>
          <leafValues>
            -4.5044522732496262e-02 3.7668040394783020e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 765 -1.8486939370632172e-02</internalNodes>
          <leafValues>
            -4.5869186520576477e-01 3.6696173250675201e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 561 -2.0481455139815807e-03</internalNodes>
          <leafValues>
            1.9705456495285034e-01 -8.1085532903671265e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 160 7.9915560781955719e-03</internalNodes>
          <leafValues>
            2.6794398203492165e-02 -6.0658437013626099e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 368 -4.5167207717895508e-03</internalNodes>
          <leafValues>
            -3.5664665699005127e-01 4.1606105864048004e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 429 -8.8896900415420532e-03</internalNodes>
          <leafValues>
            -5.6794744729995728e-01 2.4264462292194366e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 601 -2.7863893657922745e-02</internalNodes>
          <leafValues>
            -6.6293621063232422e-01 1.7915287986397743e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 153 1.9837494473904371e-03</internalNodes>
          <leafValues>
            -5.5686347186565399e-02 2.7396288514137268e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 624 -2.9144049622118473e-03</internalNodes>
          <leafValues>
            -4.3623712658882141e-01 3.1940482556819916e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 924 -1.1720246402546763e-03</internalNodes>
          <leafValues>
            1.5299941599369049e-01 -8.8886320590972900e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 927 2.1249109413474798e-03</internalNodes>
          <leafValues>
            -7.1360021829605103e-02 2.0698173344135284e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 602 4.6013649553060532e-03</internalNodes>
          <leafValues>
            2.5328675284981728e-02 -5.1310408115386963e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 644 -9.4112986698746681e-04</internalNodes>
          <leafValues>
            -2.9404127597808838e-01 4.4868268072605133e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 719 5.2681900560855865e-03</internalNodes>
          <leafValues>
            -6.4163528382778168e-02 2.2999708354473114e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 652 1.4232876710593700e-03</internalNodes>
          <leafValues>
            -7.8037962317466736e-02 1.9061613082885742e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 858 -1.0191567242145538e-02</internalNodes>
          <leafValues>
            -5.7409489154815674e-01 2.2581731900572777e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 547 -4.9564028158783913e-03</internalNodes>
          <leafValues>
            2.4646909534931183e-01 -5.9094201773405075e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 545 2.2057720925658941e-03</internalNodes>
          <leafValues>
            -9.8776444792747498e-02 1.9191808998584747e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 809 -4.7279503196477890e-03</internalNodes>
          <leafValues>
            -2.9638877511024475e-01 4.7132529318332672e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 905 1.8900397699326277e-03</internalNodes>
          <leafValues>
            -1.2390431761741638e-01 1.2199163436889648e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 692 -3.9616838330402970e-04</internalNodes>
          <leafValues>
            -2.0177872478961945e-01 6.7829817533493042e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 378 1.5198520850390196e-03</internalNodes>
          <leafValues>
            -5.0418090075254440e-02 2.8014704585075378e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 377 -3.0729006975889206e-03</internalNodes>
          <leafValues>
            1.6384753584861755e-01 -9.6394442021846771e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 637 3.3707641065120697e-02</internalNodes>
          <leafValues>
            3.3062599599361420e-02 -4.3530252575874329e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 993 -2.7547087520360947e-03</internalNodes>
          <leafValues>
            -6.2498420476913452e-01 2.0407166332006454e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 993 1.0800797026604414e-03</internalNodes>
          <leafValues>
            4.3235320597887039e-02 -3.1784874200820923e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 981 -2.4060246068984270e-03</internalNodes>
          <leafValues>
            1.3923163712024689e-01 -9.8239123821258545e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 727 4.6191983856260777e-03</internalNodes>
          <leafValues>
            2.3523205891251564e-02 -6.0865134000778198e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 284 2.1874131634831429e-03</internalNodes>
          <leafValues>
            -4.4655255973339081e-02 3.2406413555145264e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 137 7.9257078468799591e-03</internalNodes>
          <leafValues>
            2.8643675148487091e-02 -5.0231784582138062e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 340 9.6561573445796967e-03</internalNodes>
          <leafValues>
            -6.7481219768524170e-02 2.0780794322490692e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 180 -4.3771188706159592e-02</internalNodes>
          <leafValues>
            2.0091144740581512e-01 -8.7350860238075256e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 28 -3.9570517838001251e-02</internalNodes>
          <leafValues>
            -6.9823634624481201e-01 2.2996466606855392e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 517 -7.4827047064900398e-03</internalNodes>
          <leafValues>
            -3.2485857605934143e-01 4.2747449129819870e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 863 -9.5894857076928020e-04</internalNodes>
          <leafValues>
            1.3692225515842438e-01 -1.0624063760042191e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 495 -5.6482471525669098e-02</internalNodes>
          <leafValues>
            2.7130955457687378e-01 -5.5133864283561707e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 526 -5.5641448125243187e-03</internalNodes>
          <leafValues>
            -6.5910613536834717e-01 2.6108600199222565e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 833 4.5432001352310181e-03</internalNodes>
          <leafValues>
            -1.0277131199836731e-01 1.4715240895748138e-01</leafValues></_>
        <_>
          <internalNodes>
            0 -1 804 -1.9441416952759027e-03</internalNodes>
          <leafValues>
            1.7929133772850037e-01 -7.8247167170047760e-02</leafValues></_>
        <_>
          <internalNodes>
            0 -1 615 1.5584268840029836e-03</internalNodes>
          <leafValues>
            5.2101351320743561e-02 -2.7727204561233521e-01</leafValues></_></weakClassifiers></_></stages>
  <features>
    <_>
      <rects>
        <_>
          0 0 6 1 -1.</_>
        <_>
          3 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 1 -1.</_>
        <_>
          4 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 2 -1.</_>
        <_>
          4 0 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 6 -1.</_>
        <_>
          0 0 4 3 2.</_>
        <_>
          4 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 8 12 -1.</_>
        <_>
          0 0 4 6 2.</_>
        <_>
          4 6 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 10 1 -1.</_>
        <_>
          5 0 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 10 6 -1.</_>
        <_>
          0 0 5 3 2.</_>
        <_>
          5 3 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 1 -1.</_>
        <_>
          6 0 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 2 -1.</_>
        <_>
          6 0 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 14 8 -1.</_>
        <_>
          0 0 7 4 2.</_>
        <_>
          7 4 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 16 8 -1.</_>
        <_>
          0 0 8 4 2.</_>
        <_>
          8 4 8 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 16 10 -1.</_>
        <_>
          0 0 8 5 2.</_>
        <_>
          8 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 24 1 -1.</_>
        <_>
          12 0 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 0 13 10 -1.</_>
        <_>
          0 5 13 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 16 10 -1.</_>
        <_>
          0 1 8 5 2.</_>
        <_>
          8 6 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 1 13 15 -1.</_>
        <_>
          0 6 13 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 2 8 12 -1.</_>
        <_>
          0 2 4 6 2.</_>
        <_>
          4 8 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 2 10 4 -1.</_>
        <_>
          0 2 5 2 2.</_>
        <_>
          5 4 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 4 24 2 -1.</_>
        <_>
          0 4 12 1 2.</_>
        <_>
          12 5 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 5 4 9 -1.</_>
        <_>
          0 8 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 5 24 2 -1.</_>
        <_>
          0 5 12 1 2.</_>
        <_>
          12 6 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 5 24 4 -1.</_>
        <_>
          0 5 12 2 2.</_>
        <_>
          12 7 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 5 8 -1.</_>
        <_>
          0 8 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 22 17 -1.</_>
        <_>
          11 6 11 17 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 24 2 -1.</_>
        <_>
          0 6 12 1 2.</_>
        <_>
          12 7 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 6 14 8 -1.</_>
        <_>
          0 10 14 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 2 3 -1.</_>
        <_>
          0 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 6 16 -1.</_>
        <_>
          3 7 3 16 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 4 9 -1.</_>
        <_>
          0 10 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 8 17 -1.</_>
        <_>
          4 7 4 17 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 7 24 2 -1.</_>
        <_>
          6 7 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 4 16 -1.</_>
        <_>
          2 8 2 16 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 8 24 6 -1.</_>
        <_>
          0 8 12 3 2.</_>
        <_>
          12 11 12 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 1 3 -1.</_>
        <_>
          0 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 7 2 -1.</_>
        <_>
          0 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 8 2 -1.</_>
        <_>
          0 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 22 2 -1.</_>
        <_>
          0 9 11 1 2.</_>
        <_>
          11 10 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 24 2 -1.</_>
        <_>
          0 9 12 1 2.</_>
        <_>
          12 10 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 9 24 4 -1.</_>
        <_>
          0 9 12 2 2.</_>
        <_>
          12 11 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 2 2 -1.</_>
        <_>
          0 11 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 4 10 -1.</_>
        <_>
          2 10 2 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 4 3 -1.</_>
        <_>
          0 11 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 5 3 -1.</_>
        <_>
          0 11 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 22 2 -1.</_>
        <_>
          0 10 11 1 2.</_>
        <_>
          11 11 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 24 2 -1.</_>
        <_>
          0 10 12 1 2.</_>
        <_>
          12 11 12 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 24 4 -1.</_>
        <_>
          0 10 12 2 2.</_>
        <_>
          12 12 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 10 24 14 -1.</_>
        <_>
          12 10 12 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 3 3 -1.</_>
        <_>
          0 12 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 6 8 -1.</_>
        <_>
          0 11 3 4 2.</_>
        <_>
          3 15 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 11 24 4 -1.</_>
        <_>
          0 11 12 2 2.</_>
        <_>
          12 13 12 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 12 18 7 -1.</_>
        <_>
          9 12 9 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 12 22 2 -1.</_>
        <_>
          0 12 11 1 2.</_>
        <_>
          11 13 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 12 24 6 -1.</_>
        <_>
          12 12 12 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 13 24 3 -1.</_>
        <_>
          6 13 12 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 14 8 7 -1.</_>
        <_>
          4 14 4 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 14 12 10 -1.</_>
        <_>
          0 14 6 5 2.</_>
        <_>
          6 19 6 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 14 18 8 -1.</_>
        <_>
          6 14 6 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 14 20 10 -1.</_>
        <_>
          10 14 10 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 15 3 8 -1.</_>
        <_>
          1 15 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 16 3 7 -1.</_>
        <_>
          1 16 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 19 6 3 -1.</_>
        <_>
          0 20 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 19 9 3 -1.</_>
        <_>
          0 20 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 21 6 3 -1.</_>
        <_>
          0 22 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          0 21 7 3 -1.</_>
        <_>
          0 22 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 1 4 -1.</_>
        <_>
          1 2 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 12 3 -1.</_>
        <_>
          4 0 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 8 6 -1.</_>
        <_>
          1 0 4 3 2.</_>
        <_>
          5 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 8 4 -1.</_>
        <_>
          5 0 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 0 22 2 -1.</_>
        <_>
          1 0 11 1 2.</_>
        <_>
          12 1 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 21 15 -1.</_>
        <_>
          8 8 7 5 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 3 11 3 -1.</_>
        <_>
          1 4 11 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 5 3 3 -1.</_>
        <_>
          1 6 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 5 21 6 -1.</_>
        <_>
          8 7 7 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 5 22 2 -1.</_>
        <_>
          1 5 11 1 2.</_>
        <_>
          12 6 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 4 3 -1.</_>
        <_>
          1 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 5 3 -1.</_>
        <_>
          1 7 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 22 2 -1.</_>
        <_>
          1 6 11 1 2.</_>
        <_>
          12 7 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 22 17 -1.</_>
        <_>
          12 6 11 17 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 6 20 3 -1.</_>
        <_>
          1 7 20 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 12 6 -1.</_>
        <_>
          5 9 4 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 8 6 -1.</_>
        <_>
          1 9 8 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 20 4 -1.</_>
        <_>
          1 7 10 2 2.</_>
        <_>
          11 9 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 7 22 12 -1.</_>
        <_>
          1 11 22 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 8 2 -1.</_>
        <_>
          1 8 4 1 2.</_>
        <_>
          5 9 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 9 3 -1.</_>
        <_>
          1 9 9 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 8 22 4 -1.</_>
        <_>
          1 8 11 2 2.</_>
        <_>
          12 10 11 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 9 20 2 -1.</_>
        <_>
          1 9 10 1 2.</_>
        <_>
          11 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 4 3 -1.</_>
        <_>
          3 10 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 4 4 -1.</_>
        <_>
          1 11 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 22 2 -1.</_>
        <_>
          1 10 11 1 2.</_>
        <_>
          12 11 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 10 21 4 -1.</_>
        <_>
          1 11 21 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 11 3 13 -1.</_>
        <_>
          2 11 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 13 3 10 -1.</_>
        <_>
          2 13 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 14 22 2 -1.</_>
        <_>
          1 14 11 1 2.</_>
        <_>
          12 15 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          1 16 3 1 -1.</_>
        <_>
          2 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 17 4 1 -1.</_>
        <_>
          2 18 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          1 19 4 1 -1.</_>
        <_>
          2 20 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 0 4 1 -1.</_>
        <_>
          4 0 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 12 14 -1.</_>
        <_>
          6 0 4 14 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 20 1 -1.</_>
        <_>
          7 0 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 0 22 1 -1.</_>
        <_>
          13 0 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 2 22 2 -1.</_>
        <_>
          2 2 11 1 2.</_>
        <_>
          13 3 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 2 22 10 -1.</_>
        <_>
          2 2 11 5 2.</_>
        <_>
          13 7 11 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 3 20 1 -1.</_>
        <_>
          7 3 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 3 20 2 -1.</_>
        <_>
          2 3 10 1 2.</_>
        <_>
          12 4 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 3 3 -1.</_>
        <_>
          2 5 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 4 20 2 -1.</_>
        <_>
          2 4 10 1 2.</_>
        <_>
          12 5 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 5 2 3 -1.</_>
        <_>
          2 6 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 5 20 2 -1.</_>
        <_>
          2 5 10 1 2.</_>
        <_>
          12 6 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 20 2 -1.</_>
        <_>
          2 6 10 1 2.</_>
        <_>
          12 7 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 6 21 18 -1.</_>
        <_>
          2 15 21 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 6 2 -1.</_>
        <_>
          2 7 3 1 2.</_>
        <_>
          5 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 9 6 -1.</_>
        <_>
          5 9 3 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 7 3 -1.</_>
        <_>
          2 8 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 18 2 -1.</_>
        <_>
          2 8 18 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 18 3 -1.</_>
        <_>
          2 8 18 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 7 21 4 -1.</_>
        <_>
          2 8 21 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 4 2 -1.</_>
        <_>
          4 8 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 8 22 2 -1.</_>
        <_>
          2 8 11 1 2.</_>
        <_>
          13 9 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 9 7 2 -1.</_>
        <_>
          2 9 7 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 9 20 3 -1.</_>
        <_>
          2 10 20 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 11 22 2 -1.</_>
        <_>
          2 11 11 1 2.</_>
        <_>
          13 12 11 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 12 22 7 -1.</_>
        <_>
          13 12 11 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 12 19 10 -1.</_>
        <_>
          2 17 19 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 13 3 8 -1.</_>
        <_>
          3 13 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 13 20 10 -1.</_>
        <_>
          12 13 10 10 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 15 6 2 -1.</_>
        <_>
          5 15 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 15 6 3 -1.</_>
        <_>
          5 15 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 15 20 4 -1.</_>
        <_>
          2 15 10 2 2.</_>
        <_>
          12 17 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 16 6 6 -1.</_>
        <_>
          2 16 3 3 2.</_>
        <_>
          5 19 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 17 3 1 -1.</_>
        <_>
          3 18 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          2 18 3 5 -1.</_>
        <_>
          3 18 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          2 21 12 3 -1.</_>
        <_>
          8 21 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 2 20 1 -1.</_>
        <_>
          3 2 10 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 3 8 6 -1.</_>
        <_>
          5 3 4 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 6 4 -1.</_>
        <_>
          3 4 3 2 2.</_>
        <_>
          6 6 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 4 18 2 -1.</_>
        <_>
          3 4 9 1 2.</_>
        <_>
          12 5 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 20 2 -1.</_>
        <_>
          3 5 10 1 2.</_>
        <_>
          13 6 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 5 20 6 -1.</_>
        <_>
          3 5 10 3 2.</_>
        <_>
          13 8 10 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 3 3 -1.</_>
        <_>
          3 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 16 8 -1.</_>
        <_>
          3 8 16 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 6 19 6 -1.</_>
        <_>
          3 8 19 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 5 4 -1.</_>
        <_>
          3 8 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 18 6 -1.</_>
        <_>
          3 7 9 3 2.</_>
        <_>
          12 10 9 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 17 6 -1.</_>
        <_>
          3 9 17 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 7 19 2 -1.</_>
        <_>
          3 8 19 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 8 18 4 -1.</_>
        <_>
          3 8 9 2 2.</_>
        <_>
          12 10 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 8 20 4 -1.</_>
        <_>
          3 8 10 2 2.</_>
        <_>
          13 10 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 3 1 -1.</_>
        <_>
          4 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 3 3 -1.</_>
        <_>
          4 10 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 9 8 9 -1.</_>
        <_>
          3 12 8 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 20 2 -1.</_>
        <_>
          3 9 10 1 2.</_>
        <_>
          13 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 9 19 9 -1.</_>
        <_>
          3 12 19 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 3 1 -1.</_>
        <_>
          4 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 3 1 -1.</_>
        <_>
          4 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 10 3 2 -1.</_>
        <_>
          4 11 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 10 2 4 -1.</_>
        <_>
          3 11 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 8 3 -1.</_>
        <_>
          3 11 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 10 18 4 -1.</_>
        <_>
          3 10 9 2 2.</_>
        <_>
          12 12 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 1 -1.</_>
        <_>
          4 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 11 3 8 -1.</_>
        <_>
          4 11 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 4 8 -1.</_>
        <_>
          3 15 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 18 2 -1.</_>
        <_>
          3 11 9 1 2.</_>
        <_>
          12 12 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 11 10 2 -1.</_>
        <_>
          3 11 10 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 12 3 2 -1.</_>
        <_>
          4 13 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 12 8 12 -1.</_>
        <_>
          3 16 8 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 15 4 3 -1.</_>
        <_>
          5 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 16 3 1 -1.</_>
        <_>
          4 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          3 16 6 4 -1.</_>
        <_>
          3 16 3 2 2.</_>
        <_>
          6 18 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 16 8 6 -1.</_>
        <_>
          3 16 4 3 2.</_>
        <_>
          7 19 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          3 20 3 4 -1.</_>
        <_>
          4 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 0 6 4 -1.</_>
        <_>
          6 2 2 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 2 3 2 -1.</_>
        <_>
          5 2 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 2 16 2 -1.</_>
        <_>
          4 2 8 1 2.</_>
        <_>
          12 3 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 6 1 -1.</_>
        <_>
          6 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 9 3 -1.</_>
        <_>
          7 3 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 16 2 -1.</_>
        <_>
          4 3 8 1 2.</_>
        <_>
          12 4 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 9 6 -1.</_>
        <_>
          4 6 9 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 3 16 8 -1.</_>
        <_>
          4 7 16 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 1 4 -1.</_>
        <_>
          4 6 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 9 4 -1.</_>
        <_>
          7 7 3 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 4 16 2 -1.</_>
        <_>
          4 4 8 1 2.</_>
        <_>
          12 5 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 18 6 -1.</_>
        <_>
          4 6 18 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 4 20 6 -1.</_>
        <_>
          4 6 20 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 4 5 -1.</_>
        <_>
          6 5 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 16 6 -1.</_>
        <_>
          4 5 8 3 2.</_>
        <_>
          12 8 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 5 15 6 -1.</_>
        <_>
          4 7 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 1 3 -1.</_>
        <_>
          4 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 2 3 -1.</_>
        <_>
          4 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 6 2 -1.</_>
        <_>
          6 6 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 3 3 -1.</_>
        <_>
          4 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 6 6 2 -1.</_>
        <_>
          4 6 3 1 2.</_>
        <_>
          7 7 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 4 3 -1.</_>
        <_>
          4 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 15 6 -1.</_>
        <_>
          4 9 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 16 6 -1.</_>
        <_>
          4 9 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 7 17 3 -1.</_>
        <_>
          4 8 17 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 3 3 -1.</_>
        <_>
          5 9 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 2 3 -1.</_>
        <_>
          4 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 5 4 -1.</_>
        <_>
          4 9 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 8 18 4 -1.</_>
        <_>
          4 8 9 2 2.</_>
        <_>
          13 10 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 1 -1.</_>
        <_>
          5 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 3 1 -1.</_>
        <_>
          5 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 2 -1.</_>
        <_>
          4 9 1 1 2.</_>
        <_>
          5 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 2 4 -1.</_>
        <_>
          4 9 1 2 2.</_>
        <_>
          5 11 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 3 2 -1.</_>
        <_>
          4 9 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 9 6 6 -1.</_>
        <_>
          4 9 3 3 2.</_>
        <_>
          7 12 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 16 1 -1.</_>
        <_>
          8 9 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 16 2 -1.</_>
        <_>
          4 9 8 1 2.</_>
        <_>
          12 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 18 2 -1.</_>
        <_>
          4 9 9 1 2.</_>
        <_>
          13 10 9 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 9 11 4 -1.</_>
        <_>
          4 9 11 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 2 2 -1.</_>
        <_>
          4 10 1 1 2.</_>
        <_>
          5 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 3 1 -1.</_>
        <_>
          5 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 3 2 -1.</_>
        <_>
          5 11 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 3 14 -1.</_>
        <_>
          5 10 1 14 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 10 9 4 -1.</_>
        <_>
          4 10 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 10 4 -1.</_>
        <_>
          4 10 10 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 10 16 6 -1.</_>
        <_>
          4 12 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 1 -1.</_>
        <_>
          5 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 2 -1.</_>
        <_>
          5 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 4 -1.</_>
        <_>
          5 11 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 11 3 10 -1.</_>
        <_>
          4 16 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 1 -1.</_>
        <_>
          5 13 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 12 3 2 -1.</_>
        <_>
          5 12 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 1 6 -1.</_>
        <_>
          4 15 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 12 2 8 -1.</_>
        <_>
          4 16 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 13 3 1 -1.</_>
        <_>
          5 13 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 13 4 3 -1.</_>
        <_>
          6 13 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 13 9 5 -1.</_>
        <_>
          7 13 3 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 14 4 1 -1.</_>
        <_>
          6 14 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 15 3 2 -1.</_>
        <_>
          5 16 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 15 4 3 -1.</_>
        <_>
          6 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 15 9 4 -1.</_>
        <_>
          7 15 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 15 4 4 -1.</_>
        <_>
          4 16 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 17 3 1 -1.</_>
        <_>
          5 18 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          4 18 3 6 -1.</_>
        <_>
          5 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          4 20 3 4 -1.</_>
        <_>
          5 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 0 6 18 -1.</_>
        <_>
          7 0 2 18 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 4 12 -1.</_>
        <_>
          7 2 2 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 2 14 2 -1.</_>
        <_>
          5 2 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 2 15 6 -1.</_>
        <_>
          5 5 15 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 1 3 -1.</_>
        <_>
          4 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 3 2 3 -1.</_>
        <_>
          4 4 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 3 4 9 -1.</_>
        <_>
          7 3 2 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 9 3 -1.</_>
        <_>
          8 4 3 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 3 14 2 -1.</_>
        <_>
          5 3 7 1 2.</_>
        <_>
          12 4 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 4 2 3 -1.</_>
        <_>
          4 5 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 4 16 2 -1.</_>
        <_>
          5 4 8 1 2.</_>
        <_>
          13 5 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 1 3 -1.</_>
        <_>
          4 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 5 4 9 -1.</_>
        <_>
          5 8 4 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 14 2 -1.</_>
        <_>
          5 5 7 1 2.</_>
        <_>
          12 6 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 5 15 6 -1.</_>
        <_>
          5 7 15 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 1 2 -1.</_>
        <_>
          5 7 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 4 4 -1.</_>
        <_>
          5 7 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 14 2 -1.</_>
        <_>
          5 6 7 1 2.</_>
        <_>
          12 7 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 14 4 -1.</_>
        <_>
          5 7 14 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 14 6 -1.</_>
        <_>
          5 8 14 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 6 15 4 -1.</_>
        <_>
          5 7 15 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 1 3 -1.</_>
        <_>
          5 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 15 -1.</_>
        <_>
          6 7 2 15 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 1 -1.</_>
        <_>
          7 7 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 2 -1.</_>
        <_>
          5 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 3 -1.</_>
        <_>
          5 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 4 -1.</_>
        <_>
          5 8 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 3 6 -1.</_>
        <_>
          5 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 4 -1.</_>
        <_>
          5 9 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 4 6 -1.</_>
        <_>
          5 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 16 4 -1.</_>
        <_>
          5 7 8 2 2.</_>
        <_>
          13 9 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 14 2 -1.</_>
        <_>
          5 8 14 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 7 16 6 -1.</_>
        <_>
          5 9 16 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 1 3 -1.</_>
        <_>
          5 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 2 2 -1.</_>
        <_>
          5 8 1 1 2.</_>
        <_>
          6 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 3 3 -1.</_>
        <_>
          4 9 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 8 3 4 -1.</_>
        <_>
          4 9 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 8 4 4 -1.</_>
        <_>
          5 9 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 4 3 -1.</_>
        <_>
          4 9 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 8 14 2 -1.</_>
        <_>
          5 8 7 1 2.</_>
        <_>
          12 9 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 16 2 -1.</_>
        <_>
          5 8 8 1 2.</_>
        <_>
          13 9 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 8 13 16 -1.</_>
        <_>
          5 12 13 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 4 4 -1.</_>
        <_>
          7 9 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 4 3 -1.</_>
        <_>
          5 10 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 4 4 -1.</_>
        <_>
          5 9 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 9 14 2 -1.</_>
        <_>
          5 9 7 1 2.</_>
        <_>
          12 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 16 2 -1.</_>
        <_>
          5 9 8 1 2.</_>
        <_>
          13 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 9 15 3 -1.</_>
        <_>
          5 10 15 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 2 2 -1.</_>
        <_>
          5 10 1 1 2.</_>
        <_>
          6 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 3 1 -1.</_>
        <_>
          6 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 10 3 14 -1.</_>
        <_>
          6 10 1 14 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 4 3 -1.</_>
        <_>
          7 10 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 12 4 -1.</_>
        <_>
          9 10 4 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 14 3 -1.</_>
        <_>
          5 11 14 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 10 16 8 -1.</_>
        <_>
          5 12 16 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 3 2 -1.</_>
        <_>
          6 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 11 9 4 -1.</_>
        <_>
          5 11 9 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 12 3 2 -1.</_>
        <_>
          6 12 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 12 14 2 -1.</_>
        <_>
          5 12 7 1 2.</_>
        <_>
          12 13 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 13 14 2 -1.</_>
        <_>
          5 13 7 1 2.</_>
        <_>
          12 14 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 13 14 10 -1.</_>
        <_>
          5 18 14 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 15 4 1 -1.</_>
        <_>
          6 16 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 15 3 2 -1.</_>
        <_>
          6 16 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          5 19 3 4 -1.</_>
        <_>
          6 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          5 20 3 4 -1.</_>
        <_>
          6 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 1 6 11 -1.</_>
        <_>
          8 1 2 11 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 1 12 2 -1.</_>
        <_>
          6 1 6 1 2.</_>
        <_>
          12 2 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 2 1 3 -1.</_>
        <_>
          5 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 2 4 6 -1.</_>
        <_>
          6 2 4 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 2 12 6 -1.</_>
        <_>
          6 2 6 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 3 1 2 -1.</_>
        <_>
          6 3 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 3 1 3 -1.</_>
        <_>
          5 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 3 6 1 -1.</_>
        <_>
          8 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 3 18 21 -1.</_>
        <_>
          15 3 9 21 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 4 1 3 -1.</_>
        <_>
          5 5 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 4 4 3 -1.</_>
        <_>
          6 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 4 5 4 -1.</_>
        <_>
          5 5 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 4 6 3 -1.</_>
        <_>
          6 5 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 1 3 -1.</_>
        <_>
          5 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 5 3 2 -1.</_>
        <_>
          7 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 3 3 -1.</_>
        <_>
          7 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 5 12 2 -1.</_>
        <_>
          6 5 6 1 2.</_>
        <_>
          12 6 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 10 2 -1.</_>
        <_>
          6 6 5 1 2.</_>
        <_>
          11 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 6 5 3 -1.</_>
        <_>
          5 7 5 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 7 1 3 -1.</_>
        <_>
          6 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 1 6 -1.</_>
        <_>
          6 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 2 -1.</_>
        <_>
          6 8 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 2 3 -1.</_>
        <_>
          6 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 3 6 -1.</_>
        <_>
          6 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 7 12 2 -1.</_>
        <_>
          6 7 6 1 2.</_>
        <_>
          12 8 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 3 1 -1.</_>
        <_>
          7 9 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 8 2 3 -1.</_>
        <_>
          6 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 2 3 -1.</_>
        <_>
          5 9 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 8 3 4 -1.</_>
        <_>
          6 9 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 3 3 -1.</_>
        <_>
          5 9 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 8 12 3 -1.</_>
        <_>
          9 8 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 12 2 -1.</_>
        <_>
          6 8 6 1 2.</_>
        <_>
          12 9 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 8 13 6 -1.</_>
        <_>
          6 10 13 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 2 2 -1.</_>
        <_>
          6 9 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 1 -1.</_>
        <_>
          9 9 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          9 9 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 3 -1.</_>
        <_>
          9 9 6 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          10 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 2 -1.</_>
        <_>
          6 9 6 1 2.</_>
        <_>
          12 10 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 9 12 3 -1.</_>
        <_>
          6 10 12 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 1 3 -1.</_>
        <_>
          6 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 3 -1.</_>
        <_>
          7 10 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 4 -1.</_>
        <_>
          7 10 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 3 -1.</_>
        <_>
          6 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 2 4 -1.</_>
        <_>
          6 11 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 3 3 -1.</_>
        <_>
          6 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 12 1 -1.</_>
        <_>
          9 10 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 12 2 -1.</_>
        <_>
          6 10 6 1 2.</_>
        <_>
          12 11 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 10 13 3 -1.</_>
        <_>
          6 11 13 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 2 3 -1.</_>
        <_>
          6 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 3 2 -1.</_>
        <_>
          6 12 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 3 3 -1.</_>
        <_>
          6 12 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 11 13 3 -1.</_>
        <_>
          6 12 13 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 13 7 4 -1.</_>
        <_>
          6 13 7 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 14 1 3 -1.</_>
        <_>
          6 15 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          6 15 3 4 -1.</_>
        <_>
          7 16 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 15 6 3 -1.</_>
        <_>
          6 15 3 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 18 3 1 -1.</_>
        <_>
          7 19 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          6 19 3 5 -1.</_>
        <_>
          7 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 0 3 4 -1.</_>
        <_>
          7 0 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 0 4 2 -1.</_>
        <_>
          7 0 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 0 10 1 -1.</_>
        <_>
          12 0 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 1 2 6 -1.</_>
        <_>
          7 1 1 3 2.</_>
        <_>
          8 4 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 1 10 1 -1.</_>
        <_>
          12 1 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 2 1 3 -1.</_>
        <_>
          6 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 2 1 4 -1.</_>
        <_>
          6 3 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 2 3 4 -1.</_>
        <_>
          6 3 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 2 6 3 -1.</_>
        <_>
          7 3 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 2 13 10 -1.</_>
        <_>
          7 7 13 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 3 1 3 -1.</_>
        <_>
          6 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 3 2 4 -1.</_>
        <_>
          6 4 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 3 4 2 -1.</_>
        <_>
          7 3 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 4 3 3 -1.</_>
        <_>
          8 4 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 4 10 2 -1.</_>
        <_>
          7 4 5 1 2.</_>
        <_>
          12 5 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 2 -1.</_>
        <_>
          8 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 3 -1.</_>
        <_>
          8 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 5 3 5 -1.</_>
        <_>
          8 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 3 1 -1.</_>
        <_>
          8 6 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 1 4 -1.</_>
        <_>
          7 6 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 6 6 10 -1.</_>
        <_>
          9 6 2 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 10 2 -1.</_>
        <_>
          7 6 5 1 2.</_>
        <_>
          12 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 6 5 3 -1.</_>
        <_>
          6 7 5 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 6 8 4 -1.</_>
        <_>
          6 7 8 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 7 1 3 -1.</_>
        <_>
          7 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 7 2 2 -1.</_>
        <_>
          7 7 1 1 2.</_>
        <_>
          8 8 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 1 3 -1.</_>
        <_>
          7 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 2 2 -1.</_>
        <_>
          7 8 1 1 2.</_>
        <_>
          8 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 2 2 -1.</_>
        <_>
          7 8 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 8 12 7 -1.</_>
        <_>
          11 8 4 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 8 10 2 -1.</_>
        <_>
          7 8 5 1 2.</_>
        <_>
          12 9 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 1 2 -1.</_>
        <_>
          7 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 2 3 -1.</_>
        <_>
          7 9 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 9 2 3 -1.</_>
        <_>
          7 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 6 10 -1.</_>
        <_>
          7 9 3 5 2.</_>
        <_>
          10 14 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 9 10 2 -1.</_>
        <_>
          7 9 5 1 2.</_>
        <_>
          12 10 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 3 1 -1.</_>
        <_>
          8 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 1 3 -1.</_>
        <_>
          7 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 2 3 -1.</_>
        <_>
          7 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 6 5 -1.</_>
        <_>
          9 10 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 9 4 -1.</_>
        <_>
          10 10 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 10 10 2 -1.</_>
        <_>
          7 10 5 1 2.</_>
        <_>
          12 11 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 11 1 2 -1.</_>
        <_>
          7 12 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 15 5 4 -1.</_>
        <_>
          6 16 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 16 6 2 -1.</_>
        <_>
          9 18 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 16 4 2 -1.</_>
        <_>
          7 16 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 16 4 4 -1.</_>
        <_>
          6 17 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 17 3 1 -1.</_>
        <_>
          8 18 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 17 1 4 -1.</_>
        <_>
          7 19 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 17 3 6 -1.</_>
        <_>
          7 20 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 17 4 3 -1.</_>
        <_>
          6 18 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 17 5 2 -1.</_>
        <_>
          7 17 5 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 18 3 1 -1.</_>
        <_>
          8 19 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 19 3 1 -1.</_>
        <_>
          8 20 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 19 3 5 -1.</_>
        <_>
          8 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 3 1 -1.</_>
        <_>
          8 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          7 20 9 4 -1.</_>
        <_>
          7 22 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 10 4 -1.</_>
        <_>
          7 21 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          7 20 10 4 -1.</_>
        <_>
          7 22 10 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 8 1 -1.</_>
        <_>
          12 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 7 4 -1.</_>
        <_>
          8 2 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 16 6 -1.</_>
        <_>
          8 0 8 3 2.</_>
        <_>
          16 3 8 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 8 10 -1.</_>
        <_>
          8 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 16 10 -1.</_>
        <_>
          8 0 8 5 2.</_>
        <_>
          16 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 9 4 -1.</_>
        <_>
          8 1 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 0 9 10 -1.</_>
        <_>
          8 5 9 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 1 8 8 -1.</_>
        <_>
          8 5 8 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 1 12 10 -1.</_>
        <_>
          8 6 12 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 2 3 3 -1.</_>
        <_>
          9 2 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 2 2 4 -1.</_>
        <_>
          7 3 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 2 2 6 -1.</_>
        <_>
          6 4 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 3 3 8 -1.</_>
        <_>
          9 3 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 8 2 -1.</_>
        <_>
          8 4 4 1 2.</_>
        <_>
          12 5 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 4 7 15 -1.</_>
        <_>
          8 9 7 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 1 -1.</_>
        <_>
          8 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 2 -1.</_>
        <_>
          9 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 5 -1.</_>
        <_>
          9 5 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 6 -1.</_>
        <_>
          9 5 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 6 -1.</_>
        <_>
          9 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 2 7 -1.</_>
        <_>
          9 5 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 7 -1.</_>
        <_>
          9 6 1 7 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 8 -1.</_>
        <_>
          9 5 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 3 6 -1.</_>
        <_>
          8 5 3 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 5 4 6 -1.</_>
        <_>
          6 7 4 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 5 10 2 -1.</_>
        <_>
          8 5 5 1 2.</_>
        <_>
          13 6 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 5 5 3 -1.</_>
        <_>
          7 6 5 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 3 6 -1.</_>
        <_>
          9 6 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 6 3 4 -1.</_>
        <_>
          7 7 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 4 2 -1.</_>
        <_>
          8 6 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 4 3 -1.</_>
        <_>
          7 7 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 4 4 -1.</_>
        <_>
          7 7 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 6 10 2 -1.</_>
        <_>
          8 6 5 1 2.</_>
        <_>
          13 7 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 7 3 3 -1.</_>
        <_>
          7 8 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 3 4 -1.</_>
        <_>
          7 8 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 9 5 -1.</_>
        <_>
          11 10 3 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 9 8 -1.</_>
        <_>
          11 10 3 8 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 4 2 -1.</_>
        <_>
          8 7 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 4 3 -1.</_>
        <_>
          7 8 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 5 2 -1.</_>
        <_>
          8 7 5 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 8 2 -1.</_>
        <_>
          8 7 8 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 7 10 12 -1.</_>
        <_>
          8 13 10 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 8 2 2 -1.</_>
        <_>
          8 8 1 1 2.</_>
        <_>
          9 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 1 -1.</_>
        <_>
          9 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 3 1 -1.</_>
        <_>
          9 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 2 -1.</_>
        <_>
          8 10 1 1 2.</_>
        <_>
          9 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 2 2 -1.</_>
        <_>
          9 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 3 2 -1.</_>
        <_>
          9 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 4 8 -1.</_>
        <_>
          8 10 2 4 2.</_>
        <_>
          10 14 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 8 2 -1.</_>
        <_>
          8 10 4 1 2.</_>
        <_>
          12 11 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 10 15 12 -1.</_>
        <_>
          13 14 5 4 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 11 2 2 -1.</_>
        <_>
          8 11 1 1 2.</_>
        <_>
          9 12 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 13 9 3 -1.</_>
        <_>
          11 13 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 15 9 6 -1.</_>
        <_>
          11 15 3 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 15 8 6 -1.</_>
        <_>
          8 17 8 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 16 8 2 -1.</_>
        <_>
          10 16 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 16 8 3 -1.</_>
        <_>
          10 16 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 3 3 -1.</_>
        <_>
          9 18 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 17 8 3 -1.</_>
        <_>
          10 17 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 17 9 6 -1.</_>
        <_>
          8 19 9 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 18 3 6 -1.</_>
        <_>
          9 18 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 19 3 1 -1.</_>
        <_>
          9 20 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          8 19 3 4 -1.</_>
        <_>
          9 19 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 19 7 3 -1.</_>
        <_>
          8 20 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 19 9 4 -1.</_>
        <_>
          8 20 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 20 3 3 -1.</_>
        <_>
          9 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 20 16 4 -1.</_>
        <_>
          8 20 8 2 2.</_>
        <_>
          16 22 8 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          8 21 3 3 -1.</_>
        <_>
          9 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 1 2 -1.</_>
        <_>
          9 1 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 3 6 -1.</_>
        <_>
          7 2 3 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 3 -1.</_>
        <_>
          12 0 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 6 9 -1.</_>
        <_>
          9 0 3 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 0 8 9 -1.</_>
        <_>
          9 0 4 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 0 5 4 -1.</_>
        <_>
          9 2 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 5 8 -1.</_>
        <_>
          9 4 5 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 14 12 -1.</_>
        <_>
          9 0 7 6 2.</_>
        <_>
          16 6 7 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 8 10 -1.</_>
        <_>
          9 5 8 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 0 15 18 -1.</_>
        <_>
          9 6 15 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 2 8 -1.</_>
        <_>
          9 5 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 1 3 4 -1.</_>
        <_>
          8 2 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 2 2 2 -1.</_>
        <_>
          10 2 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 1 6 -1.</_>
        <_>
          9 5 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 3 10 -1.</_>
        <_>
          10 2 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 8 4 -1.</_>
        <_>
          11 4 4 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 2 6 2 -1.</_>
        <_>
          9 2 3 1 2.</_>
        <_>
          12 3 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 2 7 8 -1.</_>
        <_>
          9 6 7 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 3 3 7 -1.</_>
        <_>
          10 4 1 7 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 3 3 12 -1.</_>
        <_>
          10 3 1 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 3 6 7 -1.</_>
        <_>
          11 3 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 3 12 3 -1.</_>
        <_>
          13 4 4 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 3 11 4 -1.</_>
        <_>
          8 4 11 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 4 3 8 -1.</_>
        <_>
          10 5 1 8 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 5 2 1 -1.</_>
        <_>
          9 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 5 3 4 -1.</_>
        <_>
          10 5 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 3 6 -1.</_>
        <_>
          10 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 6 4 -1.</_>
        <_>
          11 5 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 3 3 -1.</_>
        <_>
          9 6 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 5 4 3 -1.</_>
        <_>
          8 6 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 6 3 2 -1.</_>
        <_>
          10 7 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 6 2 6 -1.</_>
        <_>
          7 8 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 6 4 3 -1.</_>
        <_>
          9 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 6 4 3 -1.</_>
        <_>
          8 7 4 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 7 2 3 -1.</_>
        <_>
          9 7 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 7 2 2 -1.</_>
        <_>
          9 7 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 7 6 2 -1.</_>
        <_>
          9 7 3 1 2.</_>
        <_>
          12 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 8 2 3 -1.</_>
        <_>
          8 9 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 8 6 5 -1.</_>
        <_>
          11 10 2 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 8 3 3 -1.</_>
        <_>
          9 9 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 8 9 9 -1.</_>
        <_>
          12 8 3 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 2 12 -1.</_>
        <_>
          9 9 1 6 2.</_>
        <_>
          10 15 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 6 3 -1.</_>
        <_>
          11 11 2 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 9 6 4 -1.</_>
        <_>
          11 11 2 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 9 3 3 -1.</_>
        <_>
          9 10 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 9 6 4 -1.</_>
        <_>
          12 9 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 3 3 -1.</_>
        <_>
          9 11 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 5 3 -1.</_>
        <_>
          9 11 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 10 14 3 -1.</_>
        <_>
          9 11 14 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 13 4 6 -1.</_>
        <_>
          9 13 2 3 2.</_>
        <_>
          11 16 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 13 9 4 -1.</_>
        <_>
          12 13 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 16 6 5 -1.</_>
        <_>
          11 16 2 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 17 6 2 -1.</_>
        <_>
          11 17 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 18 3 3 -1.</_>
        <_>
          10 19 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 19 3 2 -1.</_>
        <_>
          10 20 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          9 19 6 3 -1.</_>
        <_>
          9 20 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 19 7 3 -1.</_>
        <_>
          9 20 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 3 3 -1.</_>
        <_>
          10 20 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 5 3 -1.</_>
        <_>
          9 21 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 6 3 -1.</_>
        <_>
          9 21 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 7 3 -1.</_>
        <_>
          9 21 7 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 20 7 4 -1.</_>
        <_>
          9 22 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 21 3 3 -1.</_>
        <_>
          10 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          9 21 8 2 -1.</_>
        <_>
          9 22 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 4 1 -1.</_>
        <_>
          12 0 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 3 12 -1.</_>
        <_>
          10 4 3 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 4 8 -1.</_>
        <_>
          10 4 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 0 14 10 -1.</_>
        <_>
          10 0 7 5 2.</_>
        <_>
          17 5 7 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 2 4 6 -1.</_>
        <_>
          11 2 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 2 6 10 -1.</_>
        <_>
          10 2 3 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 3 2 5 -1.</_>
        <_>
          11 3 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 3 5 -1.</_>
        <_>
          11 4 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 4 4 19 -1.</_>
        <_>
          12 4 2 19 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 5 1 2 -1.</_>
        <_>
          10 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 5 4 3 -1.</_>
        <_>
          11 5 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 5 3 3 -1.</_>
        <_>
          10 6 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 1 3 -1.</_>
        <_>
          10 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 6 4 6 -1.</_>
        <_>
          12 6 2 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 7 4 3 -1.</_>
        <_>
          10 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 8 1 2 -1.</_>
        <_>
          10 8 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 8 2 2 -1.</_>
        <_>
          10 9 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 1 3 -1.</_>
        <_>
          9 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 9 2 3 -1.</_>
        <_>
          11 9 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 2 12 -1.</_>
        <_>
          10 9 1 6 2.</_>
        <_>
          11 15 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 2 3 -1.</_>
        <_>
          10 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 9 2 3 -1.</_>
        <_>
          9 10 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 10 3 3 -1.</_>
        <_>
          9 11 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          10 11 5 3 -1.</_>
        <_>
          10 12 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 12 14 3 -1.</_>
        <_>
          10 13 14 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 17 4 2 -1.</_>
        <_>
          11 17 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 17 2 6 -1.</_>
        <_>
          10 17 1 3 2.</_>
        <_>
          11 20 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 17 3 3 -1.</_>
        <_>
          10 18 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 17 6 2 -1.</_>
        <_>
          13 17 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 18 5 4 -1.</_>
        <_>
          10 19 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 19 5 4 -1.</_>
        <_>
          10 20 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 19 6 3 -1.</_>
        <_>
          10 20 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 3 4 -1.</_>
        <_>
          11 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 6 4 -1.</_>
        <_>
          12 20 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 5 4 -1.</_>
        <_>
          10 21 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 5 4 -1.</_>
        <_>
          10 22 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 20 14 4 -1.</_>
        <_>
          10 20 7 2 2.</_>
        <_>
          17 22 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 3 3 -1.</_>
        <_>
          11 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 21 5 2 -1.</_>
        <_>
          10 22 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 22 3 2 -1.</_>
        <_>
          11 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          10 23 3 1 -1.</_>
        <_>
          11 23 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 0 1 2 -1.</_>
        <_>
          11 0 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 0 1 4 -1.</_>
        <_>
          10 1 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 0 4 1 -1.</_>
        <_>
          13 0 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 1 1 2 -1.</_>
        <_>
          11 1 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 2 8 9 -1.</_>
        <_>
          13 4 4 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 3 -1.</_>
        <_>
          12 3 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 3 3 4 -1.</_>
        <_>
          12 3 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 3 4 -1.</_>
        <_>
          12 4 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 3 5 -1.</_>
        <_>
          12 4 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 3 7 -1.</_>
        <_>
          12 5 1 7 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 4 4 1 -1.</_>
        <_>
          13 4 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 2 3 -1.</_>
        <_>
          11 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 4 4 3 -1.</_>
        <_>
          11 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 5 3 1 -1.</_>
        <_>
          12 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 5 4 11 -1.</_>
        <_>
          13 5 2 11 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 2 3 -1.</_>
        <_>
          11 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 6 4 3 -1.</_>
        <_>
          11 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 1 3 -1.</_>
        <_>
          11 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 2 3 -1.</_>
        <_>
          11 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 2 6 -1.</_>
        <_>
          11 7 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 7 3 3 -1.</_>
        <_>
          11 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 7 3 8 -1.</_>
        <_>
          11 7 3 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 8 1 3 -1.</_>
        <_>
          11 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 8 2 3 -1.</_>
        <_>
          11 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 9 3 3 -1.</_>
        <_>
          11 10 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 4 5 -1.</_>
        <_>
          12 11 2 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 10 6 3 -1.</_>
        <_>
          13 10 2 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 6 8 -1.</_>
        <_>
          11 10 3 4 2.</_>
        <_>
          14 14 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 10 8 6 -1.</_>
        <_>
          9 12 8 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 11 1 3 -1.</_>
        <_>
          11 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 14 3 3 -1.</_>
        <_>
          10 15 3 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          11 16 3 3 -1.</_>
        <_>
          11 17 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 21 3 3 -1.</_>
        <_>
          12 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          11 22 3 2 -1.</_>
        <_>
          12 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 0 9 17 -1.</_>
        <_>
          15 0 3 17 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 2 2 14 -1.</_>
        <_>
          13 2 1 14 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 6 1 -1.</_>
        <_>
          14 3 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 8 3 -1.</_>
        <_>
          12 4 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 3 12 6 -1.</_>
        <_>
          10 5 12 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 4 2 3 -1.</_>
        <_>
          12 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 4 9 3 -1.</_>
        <_>
          15 5 3 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 3 5 -1.</_>
        <_>
          13 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 3 3 -1.</_>
        <_>
          12 6 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 9 8 -1.</_>
        <_>
          15 5 3 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 5 4 3 -1.</_>
        <_>
          12 6 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 6 2 8 -1.</_>
        <_>
          12 6 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 6 3 3 -1.</_>
        <_>
          12 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 1 3 -1.</_>
        <_>
          12 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 7 1 8 -1.</_>
        <_>
          12 7 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 7 2 6 -1.</_>
        <_>
          12 7 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 7 2 8 -1.</_>
        <_>
          12 7 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 7 3 3 -1.</_>
        <_>
          12 8 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 1 3 -1.</_>
        <_>
          12 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 8 2 3 -1.</_>
        <_>
          12 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 9 2 3 -1.</_>
        <_>
          12 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 10 6 4 -1.</_>
        <_>
          14 10 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 10 4 10 -1.</_>
        <_>
          12 10 2 5 2.</_>
        <_>
          14 15 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 11 4 8 -1.</_>
        <_>
          12 11 2 4 2.</_>
        <_>
          14 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 13 4 3 -1.</_>
        <_>
          13 13 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 14 3 2 -1.</_>
        <_>
          13 15 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          12 15 2 4 -1.</_>
        <_>
          12 15 1 2 2.</_>
        <_>
          13 17 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 15 4 5 -1.</_>
        <_>
          14 15 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 16 6 2 -1.</_>
        <_>
          14 16 2 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 19 3 5 -1.</_>
        <_>
          13 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 21 3 2 -1.</_>
        <_>
          13 21 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          12 21 3 3 -1.</_>
        <_>
          13 21 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 0 2 10 -1.</_>
        <_>
          13 0 1 5 2.</_>
        <_>
          14 5 1 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 0 4 12 -1.</_>
        <_>
          14 0 2 12 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 0 6 10 -1.</_>
        <_>
          15 0 2 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 0 11 8 -1.</_>
        <_>
          11 2 11 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 1 6 8 -1.</_>
        <_>
          15 1 2 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 2 4 2 -1.</_>
        <_>
          14 2 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 2 4 -1.</_>
        <_>
          13 3 1 2 2.</_>
        <_>
          14 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 3 6 3 -1.</_>
        <_>
          15 4 2 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 4 7 -1.</_>
        <_>
          14 4 2 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 4 3 8 -1.</_>
        <_>
          14 4 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 3 2 -1.</_>
        <_>
          14 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 3 5 -1.</_>
        <_>
          14 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 3 6 -1.</_>
        <_>
          14 5 1 6 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 3 8 -1.</_>
        <_>
          14 5 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 5 6 1 -1.</_>
        <_>
          15 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 6 7 4 -1.</_>
        <_>
          13 7 7 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 1 3 -1.</_>
        <_>
          13 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 4 8 -1.</_>
        <_>
          13 7 2 4 2.</_>
        <_>
          15 11 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 9 6 -1.</_>
        <_>
          16 9 3 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 7 6 2 -1.</_>
        <_>
          13 7 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 7 8 1 -1.</_>
        <_>
          13 7 4 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 8 2 3 -1.</_>
        <_>
          13 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 8 9 3 -1.</_>
        <_>
          16 9 3 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 8 6 8 -1.</_>
        <_>
          13 8 3 4 2.</_>
        <_>
          16 12 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 2 3 -1.</_>
        <_>
          13 10 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 6 4 -1.</_>
        <_>
          15 9 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 9 10 2 -1.</_>
        <_>
          13 10 10 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 3 1 -1.</_>
        <_>
          14 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 10 3 2 -1.</_>
        <_>
          14 10 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 11 2 3 -1.</_>
        <_>
          13 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 13 2 6 -1.</_>
        <_>
          13 13 1 3 2.</_>
        <_>
          14 16 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 13 4 6 -1.</_>
        <_>
          14 14 2 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          13 20 3 4 -1.</_>
        <_>
          14 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 22 3 2 -1.</_>
        <_>
          14 22 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          13 23 3 1 -1.</_>
        <_>
          14 23 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 3 11 -1.</_>
        <_>
          15 1 1 11 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 0 2 3 -1.</_>
        <_>
          14 1 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 6 -1.</_>
        <_>
          14 0 5 3 2.</_>
        <_>
          19 3 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 0 10 10 -1.</_>
        <_>
          14 0 5 5 2.</_>
        <_>
          19 5 5 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 1 10 6 -1.</_>
        <_>
          14 1 5 3 2.</_>
        <_>
          19 4 5 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 2 1 2 -1.</_>
        <_>
          14 2 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 2 3 8 -1.</_>
        <_>
          15 2 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 2 6 7 -1.</_>
        <_>
          16 2 2 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 3 2 4 -1.</_>
        <_>
          14 3 1 2 2.</_>
        <_>
          15 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 3 2 7 -1.</_>
        <_>
          15 3 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 2 -1.</_>
        <_>
          15 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 2 3 -1.</_>
        <_>
          15 5 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 3 -1.</_>
        <_>
          15 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 3 5 -1.</_>
        <_>
          15 5 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 2 7 -1.</_>
        <_>
          15 5 1 7 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 5 6 4 -1.</_>
        <_>
          16 5 2 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 6 6 3 -1.</_>
        <_>
          14 6 3 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 7 4 16 -1.</_>
        <_>
          15 7 2 16 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 7 6 6 -1.</_>
        <_>
          16 9 2 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 7 6 16 -1.</_>
        <_>
          16 7 2 16 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 7 9 4 -1.</_>
        <_>
          14 8 9 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 9 2 4 -1.</_>
        <_>
          14 9 1 2 2.</_>
        <_>
          15 11 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 3 1 -1.</_>
        <_>
          15 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 2 2 -1.</_>
        <_>
          15 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 10 3 3 -1.</_>
        <_>
          15 10 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 12 2 1 -1.</_>
        <_>
          15 12 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 14 3 4 -1.</_>
        <_>
          15 15 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 14 5 3 -1.</_>
        <_>
          14 15 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 15 3 3 -1.</_>
        <_>
          15 16 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          14 19 3 5 -1.</_>
        <_>
          15 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          14 21 10 1 -1.</_>
        <_>
          19 21 5 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 0 2 2 -1.</_>
        <_>
          15 0 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 0 4 2 -1.</_>
        <_>
          16 1 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 3 6 3 -1.</_>
        <_>
          15 4 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 4 2 3 -1.</_>
        <_>
          15 5 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 2 1 -1.</_>
        <_>
          15 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 5 2 2 -1.</_>
        <_>
          16 5 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 3 2 -1.</_>
        <_>
          16 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 5 3 3 -1.</_>
        <_>
          16 5 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 4 4 -1.</_>
        <_>
          15 9 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 4 6 -1.</_>
        <_>
          15 9 4 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 7 5 6 -1.</_>
        <_>
          15 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 2 2 -1.</_>
        <_>
          15 8 1 1 2.</_>
        <_>
          16 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 2 1 -1.</_>
        <_>
          15 8 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 8 1 12 -1.</_>
        <_>
          15 14 1 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 6 2 -1.</_>
        <_>
          15 8 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 8 3 16 -1.</_>
        <_>
          15 12 3 8 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 8 5 4 -1.</_>
        <_>
          15 9 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 3 3 -1.</_>
        <_>
          16 10 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 9 3 1 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 1 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 2 2 -1.</_>
        <_>
          15 10 1 1 2.</_>
        <_>
          16 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 2 2 -1.</_>
        <_>
          16 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 5 -1.</_>
        <_>
          16 10 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 3 4 -1.</_>
        <_>
          14 11 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 10 5 4 -1.</_>
        <_>
          15 11 5 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 10 5 4 -1.</_>
        <_>
          14 11 5 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          15 15 8 3 -1.</_>
        <_>
          19 15 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 15 8 8 -1.</_>
        <_>
          15 15 4 4 2.</_>
        <_>
          19 19 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 16 5 3 -1.</_>
        <_>
          15 17 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          15 18 8 6 -1.</_>
        <_>
          15 18 4 3 2.</_>
        <_>
          19 21 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 1 -1.</_>
        <_>
          20 0 4 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 2 -1.</_>
        <_>
          20 0 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 6 -1.</_>
        <_>
          16 0 4 3 2.</_>
        <_>
          20 3 4 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 0 8 8 -1.</_>
        <_>
          16 0 4 4 2.</_>
        <_>
          20 4 4 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 1 3 3 -1.</_>
        <_>
          17 2 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 1 2 1 -1.</_>
        <_>
          16 1 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 2 1 2 -1.</_>
        <_>
          16 3 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 2 3 3 -1.</_>
        <_>
          17 3 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 2 4 2 -1.</_>
        <_>
          17 3 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 2 6 1 -1.</_>
        <_>
          18 4 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 3 4 12 -1.</_>
        <_>
          17 4 2 12 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 4 6 3 -1.</_>
        <_>
          15 5 6 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 5 1 2 -1.</_>
        <_>
          16 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 5 3 2 -1.</_>
        <_>
          17 5 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 5 3 9 -1.</_>
        <_>
          16 8 3 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 6 6 2 -1.</_>
        <_>
          18 8 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 7 1 6 -1.</_>
        <_>
          16 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 3 -1.</_>
        <_>
          16 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 2 2 -1.</_>
        <_>
          16 7 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 2 -1.</_>
        <_>
          16 8 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 2 -1.</_>
        <_>
          16 7 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 7 3 6 -1.</_>
        <_>
          16 9 3 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 6 4 -1.</_>
        <_>
          16 7 3 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 7 4 3 -1.</_>
        <_>
          16 8 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 5 3 -1.</_>
        <_>
          16 8 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 5 6 -1.</_>
        <_>
          16 9 5 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 6 4 -1.</_>
        <_>
          16 8 6 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 7 7 8 -1.</_>
        <_>
          14 9 7 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 7 8 6 -1.</_>
        <_>
          16 9 8 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 1 2 -1.</_>
        <_>
          16 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 1 4 -1.</_>
        <_>
          16 9 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 2 -1.</_>
        <_>
          16 8 1 1 2.</_>
        <_>
          17 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 3 3 -1.</_>
        <_>
          17 9 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 1 -1.</_>
        <_>
          16 8 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 8 2 2 -1.</_>
        <_>
          16 8 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 8 5 6 -1.</_>
        <_>
          14 10 5 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 8 7 8 -1.</_>
        <_>
          14 10 7 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 9 3 1 -1.</_>
        <_>
          17 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 1 3 -1.</_>
        <_>
          16 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 5 3 -1.</_>
        <_>
          16 10 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 9 8 2 -1.</_>
        <_>
          16 10 8 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 1 3 -1.</_>
        <_>
          16 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 2 2 -1.</_>
        <_>
          17 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 2 3 -1.</_>
        <_>
          16 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 10 6 6 -1.</_>
        <_>
          14 12 6 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 1 -1.</_>
        <_>
          17 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 2 -1.</_>
        <_>
          17 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 13 -1.</_>
        <_>
          17 11 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 2 2 -1.</_>
        <_>
          16 12 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 2 3 -1.</_>
        <_>
          16 12 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 11 3 3 -1.</_>
        <_>
          16 12 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 14 4 2 -1.</_>
        <_>
          18 14 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 14 6 3 -1.</_>
        <_>
          19 14 3 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 19 3 5 -1.</_>
        <_>
          17 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          16 19 2 3 -1.</_>
        <_>
          15 20 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          16 19 8 3 -1.</_>
        <_>
          16 20 8 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 0 6 15 -1.</_>
        <_>
          19 2 2 15 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 0 6 1 -1.</_>
        <_>
          20 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 0 6 2 -1.</_>
        <_>
          20 0 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 2 3 3 -1.</_>
        <_>
          18 3 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 2 -1.</_>
        <_>
          18 3 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 9 -1.</_>
        <_>
          18 6 1 3 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 3 6 2 -1.</_>
        <_>
          19 5 2 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 3 3 8 -1.</_>
        <_>
          15 5 3 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 3 5 3 -1.</_>
        <_>
          17 4 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 4 2 6 -1.</_>
        <_>
          17 4 1 3 2.</_>
        <_>
          18 7 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 5 1 2 -1.</_>
        <_>
          17 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 5 3 4 -1.</_>
        <_>
          18 6 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 5 6 2 -1.</_>
        <_>
          17 5 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 5 7 3 -1.</_>
        <_>
          16 6 7 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 6 2 3 -1.</_>
        <_>
          17 6 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 6 3 4 -1.</_>
        <_>
          18 7 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 6 3 3 -1.</_>
        <_>
          17 7 3 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 6 6 1 -1.</_>
        <_>
          17 6 3 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 3 -1.</_>
        <_>
          17 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 3 -1.</_>
        <_>
          16 8 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 1 6 -1.</_>
        <_>
          17 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 6 -1.</_>
        <_>
          18 9 1 2 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 2 -1.</_>
        <_>
          18 8 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 3 -1.</_>
        <_>
          17 7 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 3 3 -1.</_>
        <_>
          18 8 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 7 2 3 -1.</_>
        <_>
          17 8 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 7 6 9 -1.</_>
        <_>
          14 10 6 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 8 3 3 -1.</_>
        <_>
          18 9 1 1 9.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 2 4 -1.</_>
        <_>
          17 8 1 2 2.</_>
        <_>
          18 10 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 2 8 -1.</_>
        <_>
          17 8 1 4 2.</_>
        <_>
          18 12 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 8 3 4 -1.</_>
        <_>
          18 9 1 4 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 8 4 6 -1.</_>
        <_>
          15 10 4 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 9 3 1 -1.</_>
        <_>
          18 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 2 6 -1.</_>
        <_>
          17 9 1 3 2.</_>
        <_>
          18 12 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 6 10 -1.</_>
        <_>
          17 9 3 5 2.</_>
        <_>
          20 14 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 9 7 2 -1.</_>
        <_>
          17 10 7 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 3 1 -1.</_>
        <_>
          18 10 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 1 3 -1.</_>
        <_>
          17 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 2 3 -1.</_>
        <_>
          18 10 1 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 2 4 -1.</_>
        <_>
          18 10 1 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 10 4 2 -1.</_>
        <_>
          17 10 2 1 2.</_>
        <_>
          19 11 2 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 2 -1.</_>
        <_>
          18 11 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 11 3 3 -1.</_>
        <_>
          18 11 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 12 3 1 -1.</_>
        <_>
          18 12 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 12 6 2 -1.</_>
        <_>
          20 12 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 15 2 3 -1.</_>
        <_>
          17 16 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 15 4 3 -1.</_>
        <_>
          19 15 2 3 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 15 4 4 -1.</_>
        <_>
          17 15 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 18 2 4 -1.</_>
        <_>
          16 19 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 18 5 3 -1.</_>
        <_>
          17 19 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          17 19 1 3 -1.</_>
        <_>
          16 20 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 20 1 3 -1.</_>
        <_>
          16 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          17 20 3 4 -1.</_>
        <_>
          18 20 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 2 2 -1.</_>
        <_>
          18 0 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 0 6 1 -1.</_>
        <_>
          21 0 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 6 5 -1.</_>
        <_>
          21 0 3 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 0 6 12 -1.</_>
        <_>
          18 0 3 6 2.</_>
        <_>
          21 6 3 6 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 2 3 1 -1.</_>
        <_>
          19 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 2 4 1 -1.</_>
        <_>
          19 3 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 2 4 3 -1.</_>
        <_>
          19 3 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 4 4 3 -1.</_>
        <_>
          18 5 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 5 1 2 -1.</_>
        <_>
          18 5 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 2 3 -1.</_>
        <_>
          18 6 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 4 3 -1.</_>
        <_>
          19 7 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 4 4 -1.</_>
        <_>
          19 7 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 2 5 -1.</_>
        <_>
          18 6 1 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 4 6 -1.</_>
        <_>
          19 7 2 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 2 3 -1.</_>
        <_>
          18 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 4 1 -1.</_>
        <_>
          18 6 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 6 5 3 -1.</_>
        <_>
          18 7 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 6 6 3 -1.</_>
        <_>
          18 7 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 2 -1.</_>
        <_>
          19 8 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 7 3 5 -1.</_>
        <_>
          19 8 1 5 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 8 1 3 -1.</_>
        <_>
          18 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 2 3 -1.</_>
        <_>
          18 9 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 8 2 2 -1.</_>
        <_>
          18 8 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 9 3 1 -1.</_>
        <_>
          19 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 2 2 -1.</_>
        <_>
          18 9 1 1 2.</_>
        <_>
          19 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 3 2 -1.</_>
        <_>
          19 9 1 2 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 2 4 -1.</_>
        <_>
          18 11 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 6 9 -1.</_>
        <_>
          21 9 3 9 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 9 6 3 -1.</_>
        <_>
          18 10 6 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 1 -1.</_>
        <_>
          19 10 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 1 3 -1.</_>
        <_>
          18 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 2 2 -1.</_>
        <_>
          18 10 1 1 2.</_>
        <_>
          19 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 10 6 2 -1.</_>
        <_>
          18 11 6 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 3 4 -1.</_>
        <_>
          19 11 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 3 13 -1.</_>
        <_>
          19 11 1 13 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 2 8 -1.</_>
        <_>
          18 15 2 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 11 6 1 -1.</_>
        <_>
          21 11 3 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 6 2 -1.</_>
        <_>
          21 12 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 12 3 8 -1.</_>
        <_>
          18 16 3 4 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 13 2 4 -1.</_>
        <_>
          18 15 2 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 14 4 4 -1.</_>
        <_>
          18 16 4 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 15 4 5 -1.</_>
        <_>
          20 15 2 5 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          18 16 2 4 -1.</_>
        <_>
          18 16 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 17 2 5 -1.</_>
        <_>
          18 17 1 5 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 18 1 3 -1.</_>
        <_>
          17 19 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          18 20 1 3 -1.</_>
        <_>
          17 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 0 2 3 -1.</_>
        <_>
          19 0 1 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 2 3 1 -1.</_>
        <_>
          20 3 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 2 4 3 -1.</_>
        <_>
          20 3 2 3 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 3 3 1 -1.</_>
        <_>
          20 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 4 1 2 -1.</_>
        <_>
          19 4 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 4 3 1 -1.</_>
        <_>
          20 5 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 4 1 3 -1.</_>
        <_>
          18 5 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 4 1 4 -1.</_>
        <_>
          19 6 1 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 4 5 9 -1.</_>
        <_>
          19 7 5 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 5 3 1 -1.</_>
        <_>
          20 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 6 1 3 -1.</_>
        <_>
          19 7 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 1 3 -1.</_>
        <_>
          18 7 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 6 2 3 -1.</_>
        <_>
          19 7 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 6 5 9 -1.</_>
        <_>
          19 9 5 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 7 1 3 -1.</_>
        <_>
          19 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 7 3 4 -1.</_>
        <_>
          20 7 1 4 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 7 2 4 -1.</_>
        <_>
          19 7 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 8 1 3 -1.</_>
        <_>
          19 9 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 8 3 3 -1.</_>
        <_>
          20 8 1 3 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 9 2 1 -1.</_>
        <_>
          20 9 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 9 2 2 -1.</_>
        <_>
          19 9 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 10 2 2 -1.</_>
        <_>
          19 10 1 1 2.</_>
        <_>
          20 11 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 10 3 4 -1.</_>
        <_>
          19 11 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 12 4 8 -1.</_>
        <_>
          20 13 2 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 12 3 10 -1.</_>
        <_>
          20 12 1 10 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 12 3 12 -1.</_>
        <_>
          20 12 1 12 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 13 3 9 -1.</_>
        <_>
          20 13 1 9 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          19 14 4 6 -1.</_>
        <_>
          20 15 2 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 15 3 6 -1.</_>
        <_>
          20 16 1 6 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 17 1 3 -1.</_>
        <_>
          18 18 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 18 1 3 -1.</_>
        <_>
          18 19 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 19 1 3 -1.</_>
        <_>
          18 20 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 19 1 4 -1.</_>
        <_>
          18 20 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 20 1 3 -1.</_>
        <_>
          18 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          19 21 5 3 -1.</_>
        <_>
          19 22 5 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 0 4 4 -1.</_>
        <_>
          19 1 4 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 3 3 1 -1.</_>
        <_>
          21 4 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 3 3 2 -1.</_>
        <_>
          21 4 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 4 1 3 -1.</_>
        <_>
          20 5 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 4 3 1 -1.</_>
        <_>
          21 5 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 4 3 2 -1.</_>
        <_>
          21 5 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 5 3 1 -1.</_>
        <_>
          21 6 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 6 4 3 -1.</_>
        <_>
          20 7 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 8 3 2 -1.</_>
        <_>
          21 9 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 8 3 3 -1.</_>
        <_>
          21 9 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 9 3 2 -1.</_>
        <_>
          21 10 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 9 4 10 -1.</_>
        <_>
          20 9 2 10 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 10 1 3 -1.</_>
        <_>
          19 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 10 2 2 -1.</_>
        <_>
          20 10 2 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 11 4 9 -1.</_>
        <_>
          20 11 2 9 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 14 4 6 -1.</_>
        <_>
          21 15 2 6 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 14 2 7 -1.</_>
        <_>
          20 14 1 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 15 3 4 -1.</_>
        <_>
          19 16 3 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 16 4 4 -1.</_>
        <_>
          21 17 2 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 17 3 5 -1.</_>
        <_>
          21 17 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          20 20 1 3 -1.</_>
        <_>
          19 21 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          20 20 4 3 -1.</_>
        <_>
          20 21 4 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 1 2 16 -1.</_>
        <_>
          21 1 2 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 1 3 4 -1.</_>
        <_>
          21 2 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 3 3 2 -1.</_>
        <_>
          22 4 1 2 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 4 3 3 -1.</_>
        <_>
          22 5 1 3 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 10 1 3 -1.</_>
        <_>
          20 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 10 2 2 -1.</_>
        <_>
          21 10 1 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 10 3 4 -1.</_>
        <_>
          21 11 3 2 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 11 1 2 -1.</_>
        <_>
          21 11 1 1 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 15 2 3 -1.</_>
        <_>
          20 16 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 16 1 3 -1.</_>
        <_>
          20 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 16 3 8 -1.</_>
        <_>
          22 16 1 8 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 16 2 3 -1.</_>
        <_>
          20 17 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 17 1 3 -1.</_>
        <_>
          20 18 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          21 17 3 7 -1.</_>
        <_>
          22 17 1 7 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          21 19 3 5 -1.</_>
        <_>
          22 19 1 5 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          22 1 2 4 -1.</_>
        <_>
          21 2 2 2 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 2 1 16 -1.</_>
        <_>
          22 2 1 8 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 9 2 4 -1.</_>
        <_>
          22 9 1 4 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 10 1 3 -1.</_>
        <_>
          21 11 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 10 2 7 -1.</_>
        <_>
          22 10 1 7 2.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 10 2 3 -1.</_>
        <_>
          22 11 2 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          22 10 2 3 -1.</_>
        <_>
          21 11 2 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 11 1 3 -1.</_>
        <_>
          21 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 12 1 3 -1.</_>
        <_>
          21 13 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 13 1 3 -1.</_>
        <_>
          21 14 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          22 16 1 3 -1.</_>
        <_>
          21 17 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          23 7 1 3 -1.</_>
        <_>
          23 8 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 10 1 3 -1.</_>
        <_>
          23 11 1 1 3.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 11 1 2 -1.</_>
        <_>
          23 12 1 1 2.</_></rects>
      <tilted>0</tilted></_>
    <_>
      <rects>
        <_>
          23 11 1 3 -1.</_>
        <_>
          22 12 1 1 3.</_></rects>
      <tilted>1</tilted></_>
    <_>
      <rects>
        <_>
          23 15 1 4 -1.</_>
        <_>
          22 16 1 2 2.</_></rects>
      <tilted>1</tilted></_></features></cascade>
</opencv_storage>
