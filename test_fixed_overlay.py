"""Test script untuk memverifikasi perbaikan overlay

Script ini menguji apakah overlay renderer yang diperbaiki dapat:
1. Membuat skeleton overlay dengan transparansi yang benar
2. Membuat heatmap overlay yang terlihat
3. <PERSON><PERSON><PERSON><PERSON><PERSON> encoding base64 yang valid
4. Menampilkan fallback yang proper saat error
"""

import logging
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_overlay_rendering():
    """Test overlay rendering functionality."""
    try:
        # Import the fixed overlay renderer
        from src.presentation.gpu.fixed_overlay_renderer import FixedOverlayRenderer
        from PIL import Image
        import flet as ft
        
        logger.info("🧪 TESTING FIXED OVERLAY RENDERER")
        logger.info("=" * 50)
        
        # Create mock overlay control
        class MockOverlayControl:
            def __init__(self):
                self.src_base64 = None
        
        mock_control = MockOverlayControl()
        
        # Initialize renderer
        renderer = FixedOverlayRenderer(mock_control, 640, 480, test_mode=False)
        logger.info("✅ Renderer initialized successfully")
        
        # Test 1: Skeleton rendering
        logger.info("\n📊 TEST 1: Skeleton Overlay Rendering")
        test_keypoints = {
            'nose': (0.5, 0.2, 0.9),
            'left_shoulder': (0.3, 0.3, 0.9),
            'right_shoulder': (0.7, 0.3, 0.9),
            'left_elbow': (0.2, 0.5, 0.8),
            'right_elbow': (0.8, 0.5, 0.8),
            'left_wrist': (0.1, 0.7, 0.7),
            'right_wrist': (0.9, 0.7, 0.7),
            'left_hip': (0.4, 0.7, 0.9),
            'right_hip': (0.6, 0.7, 0.9),
            'left_knee': (0.35, 0.85, 0.8),
            'right_knee': (0.65, 0.85, 0.8),
            'left_ankle': (0.3, 0.95, 0.7),
            'right_ankle': (0.7, 0.95, 0.7),
        }
        
        # Test different risk levels
        risk_levels = ['low', 'medium', 'high', 'critical']
        for risk_level in risk_levels:
            success = renderer.render_skeleton(test_keypoints, risk_level)
            if success and mock_control.src_base64:
                logger.info(f"   ✅ {risk_level.upper()} risk skeleton: PASSED")
                logger.info(f"      Base64 length: {len(mock_control.src_base64)}")
                
                # Verify it's a valid data URL
                if mock_control.src_base64.startswith('data:image/png;base64,'):
                    logger.info(f"      Format: Valid PNG data URL")
                else:
                    logger.warning(f"      Format: Invalid data URL format")
            else:
                logger.error(f"   ❌ {risk_level.upper()} risk skeleton: FAILED")
        
        # Test 2: Heatmap rendering
        logger.info("\n🔥 TEST 2: Heatmap Overlay Rendering")
        
        # Test demo heatmap
        for risk_level in risk_levels:
            success = renderer.render_heatmap(None, risk_level)
            if success and mock_control.src_base64:
                logger.info(f"   ✅ {risk_level.upper()} risk heatmap: PASSED")
                logger.info(f"      Base64 length: {len(mock_control.src_base64)}")
            else:
                logger.error(f"   ❌ {risk_level.upper()} risk heatmap: FAILED")
        
        # Test 3: Clear overlay
        logger.info("\n🧹 TEST 3: Clear Overlay")
        success = renderer.clear_overlay()
        if success and mock_control.src_base64:
            logger.info("   ✅ Clear overlay: PASSED")
            logger.info(f"      Base64 length: {len(mock_control.src_base64)}")
        else:
            logger.error("   ❌ Clear overlay: FAILED")
        
        # Test 4: Enable/Disable functionality
        logger.info("\n🔄 TEST 4: Enable/Disable Functionality")
        
        # Disable renderer
        renderer.set_enabled(False)
        success = renderer.render_skeleton(test_keypoints, 'high')
        if not success:
            logger.info("   ✅ Disabled rendering: PASSED (correctly blocked)")
        else:
            logger.warning("   ⚠️ Disabled rendering: Unexpected success")
        
        # Re-enable renderer
        renderer.set_enabled(True)
        success = renderer.render_skeleton(test_keypoints, 'high')
        if success:
            logger.info("   ✅ Re-enabled rendering: PASSED")
        else:
            logger.error("   ❌ Re-enabled rendering: FAILED")
        
        # Test 5: Performance test
        logger.info("\n⚡ TEST 5: Performance Test")
        import time
        
        start_time = time.time()
        render_count = 50
        
        for i in range(render_count):
            # Alternate between skeleton and heatmap
            if i % 2 == 0:
                renderer.render_skeleton(test_keypoints, 'medium')
            else:
                renderer.render_heatmap(None, 'medium')
        
        end_time = time.time()
        total_time = end_time - start_time
        avg_time = (total_time / render_count) * 1000  # Convert to ms
        
        logger.info(f"   ✅ Performance test completed")
        logger.info(f"      Total renders: {render_count}")
        logger.info(f"      Total time: {total_time:.2f}s")
        logger.info(f"      Average time per render: {avg_time:.1f}ms")
        
        if avg_time < 50:  # Target: under 50ms per render
            logger.info(f"      Performance: EXCELLENT (target: <50ms)")
        elif avg_time < 100:
            logger.info(f"      Performance: GOOD (target: <50ms)")
        else:
            logger.warning(f"      Performance: NEEDS IMPROVEMENT (target: <50ms)")
        
        logger.info("\n" + "=" * 50)
        logger.info("🎉 ALL OVERLAY TESTS COMPLETED")
        logger.info("✨ Fixed overlay renderer is working correctly!")
        
        return True
        
    except ImportError as e:
        logger.error(f"❌ Import error: {e}")
        logger.info("   Make sure all dependencies are installed:")
        logger.info("   - pip install pillow")
        logger.info("   - pip install flet")
        return False
        
    except Exception as e:
        logger.error(f"❌ Test failed: {e}")
        return False


def test_integration_with_main_app():
    """Test integration with the main application."""
    try:
        logger.info("\n🔗 TESTING INTEGRATION WITH MAIN APP")
        logger.info("=" * 50)
        
        # Test importing the demo
        from demo_fixed_overlay import FixedOverlayRenderer as DemoRenderer
        
        # Create test instance
        demo_renderer = DemoRenderer(640, 480)
        logger.info("✅ Demo renderer import: PASSED")
        
        # Test skeleton overlay creation
        test_keypoints = {
            'nose': (0.5, 0.2, 0.9),
            'left_shoulder': (0.3, 0.3, 0.9),
            'right_shoulder': (0.7, 0.3, 0.9),
        }
        
        skeleton_result = demo_renderer.create_skeleton_overlay(test_keypoints, 'high')
        if skeleton_result and skeleton_result.startswith('data:image/png;base64,'):
            logger.info("✅ Demo skeleton overlay: PASSED")
        else:
            logger.error("❌ Demo skeleton overlay: FAILED")
        
        # Test heatmap overlay creation
        heatmap_result = demo_renderer.create_heatmap_overlay(None, 'medium')
        if heatmap_result and heatmap_result.startswith('data:image/png;base64,'):
            logger.info("✅ Demo heatmap overlay: PASSED")
        else:
            logger.error("❌ Demo heatmap overlay: FAILED")
        
        logger.info("🎉 INTEGRATION TESTS COMPLETED")
        return True
        
    except Exception as e:
        logger.error(f"❌ Integration test failed: {e}")
        return False


if __name__ == "__main__":
    logger.info("🚀 STARTING OVERLAY FIX VERIFICATION")
    logger.info("=" * 60)
    
    # Run tests
    test1_passed = test_overlay_rendering()
    test2_passed = test_integration_with_main_app()
    
    # Summary
    logger.info("\n" + "=" * 60)
    logger.info("📋 TEST SUMMARY")
    logger.info(f"   Overlay Rendering Test: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    logger.info(f"   Integration Test: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed and test2_passed:
        logger.info("\n🎉 ALL TESTS PASSED!")
        logger.info("✨ Overlay fixes are working correctly!")
        logger.info("🚀 Ready to run: python demo_fixed_overlay.py")
    else:
        logger.error("\n❌ SOME TESTS FAILED!")
        logger.info("🔧 Check the error messages above for troubleshooting")
        sys.exit(1)
    
    logger.info("=" * 60)
