__all__: list[str] = []

import cv2
import cv2.typing
import typing as _typing


# Classes
class GraphSegmentation(cv2.Algorithm):
    # Functions
    @_typing.overload
    def processImage(self, src: cv2.typing.MatLike, dst: cv2.typing.MatLike | None = ...) -> cv2.typing.MatLike: ...
    @_typing.overload
    def processImage(self, src: cv2.UMat, dst: cv2.UMat | None = ...) -> cv2.UMat: ...

    def setSigma(self, sigma: float) -> None: ...

    def getSigma(self) -> float: ...

    def setK(self, k: float) -> None: ...

    def getK(self) -> float: ...

    def setMinSize(self, min_size: int) -> None: ...

    def getMinSize(self) -> int: ...


class SelectiveSearchSegmentationStrategy(cv2.Algorithm):
    # Functions
    @_typing.overload
    def setImage(self, img: cv2.typing.MatLike, regions: cv2.typing.MatLike, sizes: cv2.typing.MatLike, image_id: int = ...) -> None: ...
    @_typing.overload
    def setImage(self, img: cv2.UMat, regions: cv2.UMat, sizes: cv2.UMat, image_id: int = ...) -> None: ...

    def get(self, r1: int, r2: int) -> float: ...

    def merge(self, r1: int, r2: int) -> None: ...


class SelectiveSearchSegmentationStrategyColor(SelectiveSearchSegmentationStrategy):
    ...

class SelectiveSearchSegmentationStrategySize(SelectiveSearchSegmentationStrategy):
    ...

class SelectiveSearchSegmentationStrategyTexture(SelectiveSearchSegmentationStrategy):
    ...

class SelectiveSearchSegmentationStrategyFill(SelectiveSearchSegmentationStrategy):
    ...

class SelectiveSearchSegmentationStrategyMultiple(SelectiveSearchSegmentationStrategy):
    # Functions
    def addStrategy(self, g: SelectiveSearchSegmentationStrategy, weight: float) -> None: ...

    def clearStrategies(self) -> None: ...


class SelectiveSearchSegmentation(cv2.Algorithm):
    # Functions
    @_typing.overload
    def setBaseImage(self, img: cv2.typing.MatLike) -> None: ...
    @_typing.overload
    def setBaseImage(self, img: cv2.UMat) -> None: ...

    def switchToSingleStrategy(self, k: int = ..., sigma: float = ...) -> None: ...

    def switchToSelectiveSearchFast(self, base_k: int = ..., inc_k: int = ..., sigma: float = ...) -> None: ...

    def switchToSelectiveSearchQuality(self, base_k: int = ..., inc_k: int = ..., sigma: float = ...) -> None: ...

    @_typing.overload
    def addImage(self, img: cv2.typing.MatLike) -> None: ...
    @_typing.overload
    def addImage(self, img: cv2.UMat) -> None: ...

    def clearImages(self) -> None: ...

    def addGraphSegmentation(self, g: GraphSegmentation) -> None: ...

    def clearGraphSegmentations(self) -> None: ...

    def addStrategy(self, s: SelectiveSearchSegmentationStrategy) -> None: ...

    def clearStrategies(self) -> None: ...

    def process(self) -> _typing.Sequence[cv2.typing.Rect]: ...



# Functions
def createGraphSegmentation(sigma: float = ..., k: float = ..., min_size: int = ...) -> GraphSegmentation: ...

def createSelectiveSearchSegmentation() -> SelectiveSearchSegmentation: ...

def createSelectiveSearchSegmentationStrategyColor() -> SelectiveSearchSegmentationStrategyColor: ...

def createSelectiveSearchSegmentationStrategyFill() -> SelectiveSearchSegmentationStrategyFill: ...

@_typing.overload
def createSelectiveSearchSegmentationStrategyMultiple() -> SelectiveSearchSegmentationStrategyMultiple: ...
@_typing.overload
def createSelectiveSearchSegmentationStrategyMultiple(s1: SelectiveSearchSegmentationStrategy) -> SelectiveSearchSegmentationStrategyMultiple: ...
@_typing.overload
def createSelectiveSearchSegmentationStrategyMultiple(s1: SelectiveSearchSegmentationStrategy, s2: SelectiveSearchSegmentationStrategy) -> SelectiveSearchSegmentationStrategyMultiple: ...
@_typing.overload
def createSelectiveSearchSegmentationStrategyMultiple(s1: SelectiveSearchSegmentationStrategy, s2: SelectiveSearchSegmentationStrategy, s3: SelectiveSearchSegmentationStrategy) -> SelectiveSearchSegmentationStrategyMultiple: ...
@_typing.overload
def createSelectiveSearchSegmentationStrategyMultiple(s1: SelectiveSearchSegmentationStrategy, s2: SelectiveSearchSegmentationStrategy, s3: SelectiveSearchSegmentationStrategy, s4: SelectiveSearchSegmentationStrategy) -> SelectiveSearchSegmentationStrategyMultiple: ...

def createSelectiveSearchSegmentationStrategySize() -> SelectiveSearchSegmentationStrategySize: ...

def createSelectiveSearchSegmentationStrategyTexture() -> SelectiveSearchSegmentationStrategyTexture: ...


