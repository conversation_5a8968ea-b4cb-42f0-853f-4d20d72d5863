"""Real-time recommendations panel widget.

This widget displays ergonomic recommendations based on current REBA scores
and provides actionable guidance to users during assessment sessions.
"""

import flet as ft
import logging
import time
from typing import List, Dict, Any, Optional
from collections import deque

from ....core.event_bus import EventBus, EventHandler
from ....core.events import RecommendationReadyEvent, ScoresUpdatedEvent
from ....domain.ergonomics.value_objects.score import RiskLevel


logger = logging.getLogger(__name__)


class RecommendationEventHandler(EventHandler):
    """Event handler for recommendation-related events."""
    
    def __init__(self, panel: 'RecommendationsPanel'):
        self.panel = panel
    
    def handle(self, event) -> None:
        """Handle recommendation and score events."""
        if isinstance(event, RecommendationReadyEvent):
            self.panel._add_recommendation(event)
        elif isinstance(event, ScoresUpdatedEvent):
            self.panel._update_risk_status(event.scores)
    
    async def handle_async(self, event) -> None:
        """Handle events asynchronously."""
        self.handle(event)


class RecommendationsPanel:
    """Widget for displaying real-time ergonomic recommendations.
    
    Features:
    - Real-time recommendation display
    - Risk level indicators
    - Recommendation history
    - Action buttons for quick responses
    """
    
    def __init__(self, event_bus: EventBus, max_recommendations: int = 10):
        """Initialize the recommendations panel.
        
        Args:
            event_bus: Event system for receiving recommendations
            max_recommendations: Maximum number of recommendations to display
        """
        self.event_bus = event_bus
        self.max_recommendations = max_recommendations
        
        # State
        self.recommendations_history = deque(maxlen=max_recommendations)
        self.current_risk_level = RiskLevel.LOW
        self.last_recommendation_time = 0
        self.recommendation_count = 0
        
        # UI components
        self.container = None
        self.risk_indicator = None
        self.current_recommendation_text = None
        self.recommendations_list = None
        self.stats_text = None
        self.clear_button = None
        self.pause_button = None
        
        # Settings
        self.is_paused = False
        self.show_confidence = True
        self.auto_clear_old = True
        
        # Set up event handling
        self.recommendation_handler = RecommendationEventHandler(self)
        self.event_bus.subscribe(RecommendationReadyEvent, self.recommendation_handler)
        self.event_bus.subscribe(ScoresUpdatedEvent, self.recommendation_handler)
        
        # Initialize UI
        self._build_ui()
        
        logger.info("Recommendations panel initialized")
    
    def _build_ui(self) -> None:
        """Build the recommendations panel UI."""
        # Risk level indicator
        self.risk_indicator = ft.Container(
            content=ft.Row([
                ft.Icon(ft.Icons.HEALTH_AND_SAFETY, size=20),
                ft.Text("Tingkat Risiko: RENDAH", weight=ft.FontWeight.BOLD, size=16)
            ], spacing=10),
            padding=10,
            bgcolor=ft.Colors.GREEN_100,
            border_radius=8,
            border=ft.border.all(2, ft.Colors.GREEN)
        )
        
        # Current recommendation display
        self.current_recommendation_text = ft.Text(
            "No recommendations yet - start assessment to receive guidance",
            size=14,
            color=ft.Colors.GREY_700,
            text_align=ft.TextAlign.CENTER
        )
        
        current_rec_container = ft.Container(
            content=ft.Column([
                ft.Text("Rekomendasi Saat Ini", size=16, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                self.current_recommendation_text
            ], spacing=8),
            padding=15,
            bgcolor=ft.Colors.BLUE_50,
            border_radius=8,
            border=ft.border.all(1, ft.Colors.BLUE_200)
        )
        
        # Recommendations history list
        self.recommendations_list = ft.Column(
            controls=[],
            spacing=5,
            scroll=ft.ScrollMode.AUTO,
            height=200
        )
        
        history_container = ft.Container(
            content=ft.Column([
                ft.Text("Rekomendasi Terbaru", size=14, weight=ft.FontWeight.BOLD),
                ft.Divider(height=1),
                self.recommendations_list
            ], spacing=8),
            padding=10,
            border=ft.border.all(1, ft.Colors.GREY_300),
            border_radius=8
        )
        
        # Statistics and controls
        self.stats_text = ft.Text("Recommendations: 0 | Last: --", size=12, color=ft.Colors.GREY_600)
        
        self.clear_button = ft.IconButton(
            icon=ft.Icons.CLEAR_ALL,
            tooltip="Clear recommendations history",
            on_click=self._on_clear_clicked
        )
        
        self.pause_button = ft.IconButton(
            icon=ft.Icons.PAUSE,
            tooltip="Pause recommendations",
            on_click=self._on_pause_clicked
        )
        
        controls_row = ft.Row([
            ft.Container(
                content=self.stats_text,
                expand=True
            ),
            ft.Container(
                content=ft.Row([
                    self.pause_button,
                    self.clear_button
                ], spacing=6),
                alignment=ft.alignment.center_right
            )
        ], alignment=ft.MainAxisAlignment.SPACE_BETWEEN)
        
        # Quick action buttons
        quick_actions = ft.Row([
            ft.ElevatedButton(
                "Take Break",
                icon=ft.Icons.PAUSE_CIRCLE,
                on_click=self._on_break_clicked,
                bgcolor=ft.Colors.ORANGE_100,
                width=140,
                height=36
            ),
            ft.ElevatedButton(
                "Adjust Posture",
                icon=ft.Icons.ACCESSIBILITY,
                on_click=self._on_adjust_clicked,
                bgcolor=ft.Colors.BLUE_100,
                width=140,
                height=36
            ),
        ], spacing=8, alignment=ft.MainAxisAlignment.SPACE_EVENLY, wrap=True)
        
        # Main container
        self.container = ft.Container(
            content=ft.Column([
                ft.Text("Rekomendasi Real-Time", size=18, weight=ft.FontWeight.BOLD),
                ft.Divider(),
                self.risk_indicator,
                current_rec_container,
                history_container,
                controls_row,
                ft.Divider(),
                ft.Text("Quick Actions", size=14, weight=ft.FontWeight.BOLD),
                quick_actions
            ], spacing=12, scroll=ft.ScrollMode.AUTO),
            padding=15,
            border=ft.border.all(1, ft.Colors.PURPLE),
            border_radius=8,
            expand=True
        )
    
    def get_control(self) -> ft.Control:
        """Get the main UI control for this panel.
        
        Returns:
            Flet control for the recommendations panel
        """
        return self.container
    
    def _add_recommendation(self, event: RecommendationReadyEvent) -> None:
        """Add a new recommendation to the panel.
        
        Args:
            event: RecommendationReadyEvent containing recommendation data
        """
        if self.is_paused:
            return
        
        try:
            # Update current recommendation
            self.current_recommendation_text.value = event.recommendation_text
            self.current_recommendation_text.color = self._get_risk_color(event.risk_level)
            
            # Add to history
            recommendation_item = self._create_recommendation_item(event)
            self.recommendations_history.append(recommendation_item)
            
            # Update history display
            self._update_recommendations_list()
            
            # Update statistics
            self.recommendation_count += 1
            self.last_recommendation_time = time.time()
            self._update_stats_display()
            
            # Auto-clear old recommendations if enabled
            if self.auto_clear_old and len(self.recommendations_history) > self.max_recommendations:
                # Remove oldest items beyond max limit
                while len(self.recommendations_history) > self.max_recommendations:
                    self.recommendations_history.popleft()
                self._update_recommendations_list()
            
            logger.debug(f"Added recommendation: {event.recommendation_text}")
            
        except Exception as e:
            logger.error(f"Failed to add recommendation: {e}")
    
    def _update_risk_status(self, scores) -> None:
        """Update risk level indicator based on scores.
        
        Args:
            scores: ErgonomicScore object
        """
        try:
            new_risk_level = scores.get_reba_risk_level()
            
            if new_risk_level != self.current_risk_level:
                self.current_risk_level = new_risk_level
                self._update_risk_indicator()
            
        except Exception as e:
            logger.error(f"Failed to update risk status: {e}")
    
    def _update_risk_indicator(self) -> None:
        """Update the risk level indicator UI."""
        risk_text = f"Tingkat Risiko: {self.current_risk_level.value.upper()}"
        risk_color = self._get_risk_color(self.current_risk_level.value)
        
        # Update text
        if hasattr(self.risk_indicator.content, 'controls'):
            text_control = self.risk_indicator.content.controls[1]
            text_control.value = risk_text
            text_control.color = risk_color
        
        # Update container colors
        if self.current_risk_level == RiskLevel.LOW:
            self.risk_indicator.bgcolor = ft.Colors.GREEN_100
            self.risk_indicator.border = ft.border.all(2, ft.Colors.GREEN)
        elif self.current_risk_level == RiskLevel.MEDIUM:
            self.risk_indicator.bgcolor = ft.Colors.YELLOW_100
            self.risk_indicator.border = ft.border.all(2, ft.Colors.YELLOW)
        elif self.current_risk_level == RiskLevel.HIGH:
            self.risk_indicator.bgcolor = ft.Colors.ORANGE_100
            self.risk_indicator.border = ft.border.all(2, ft.Colors.ORANGE)
        else:  # CRITICAL
            self.risk_indicator.bgcolor = ft.Colors.RED_100
            self.risk_indicator.border = ft.border.all(2, ft.Colors.RED)
    
    def _create_recommendation_item(self, event: RecommendationReadyEvent) -> ft.Control:
        """Create a UI item for a recommendation.
        
        Args:
            event: RecommendationReadyEvent
            
        Returns:
            Flet control representing the recommendation
        """
        timestamp = time.strftime("%H:%M:%S", time.localtime())
        
        # Create confidence indicator if enabled
        confidence_text = ""
        if self.show_confidence and hasattr(event, 'confidence_score') and event.confidence_score:
            confidence_text = f" ({event.confidence_score:.0%})"
        
        return ft.Container(
            content=ft.Column([
                ft.Row([
                    ft.Text(timestamp, size=10, color=ft.Colors.GREY_600),
                    ft.Container(expand=True),
                    ft.Text(event.risk_level.upper(), size=10, 
                           color=self._get_risk_color(event.risk_level),
                           weight=ft.FontWeight.BOLD)
                ]),
                ft.Text(f"{event.recommendation_text}{confidence_text}", 
                       size=12, text_align=ft.TextAlign.LEFT)
            ], spacing=2),
            padding=8,
            bgcolor=ft.Colors.GREY_50,
            border_radius=4,
            border=ft.border.all(1, ft.Colors.GREY_200)
        )
    
    def _update_recommendations_list(self) -> None:
        """Update the recommendations history list display."""
        try:
            # Clear current list
            self.recommendations_list.controls.clear()
            
            # Add items from history (most recent first)
            for item in reversed(list(self.recommendations_history)):
                self.recommendations_list.controls.append(item)
            
        except Exception as e:
            logger.error(f"Failed to update recommendations list: {e}")
    
    def _update_stats_display(self) -> None:
        """Update the statistics display."""
        try:
            if self.last_recommendation_time > 0:
                last_time = time.strftime("%H:%M:%S", time.localtime(self.last_recommendation_time))
            else:
                last_time = "--"
            
            self.stats_text.value = f"Recommendations: {self.recommendation_count} | Last: {last_time}"
            
        except Exception as e:
            logger.error(f"Failed to update stats display: {e}")
    
    def _get_risk_color(self, risk_level: str) -> str:
        """Get color for risk level display.
        
        Args:
            risk_level: Risk level string
            
        Returns:
            Flet color string
        """
        color_map = {
            'low': ft.Colors.GREEN,
            'medium': ft.Colors.YELLOW_700,
            'high': ft.Colors.ORANGE,
            'critical': ft.Colors.RED
        }
        return color_map.get(risk_level.lower(), ft.Colors.GREY)
    
    # Event handlers
    def _on_clear_clicked(self, e) -> None:
        """Handle clear button click."""
        try:
            self.recommendations_history.clear()
            self.recommendations_list.controls.clear()
            self.current_recommendation_text.value = "Recommendations cleared"
            self.current_recommendation_text.color = ft.Colors.GREY_600
            
            logger.info("Recommendations history cleared")
            
        except Exception as ex:
            logger.error(f"Failed to clear recommendations: {ex}")
    
    def _on_pause_clicked(self, e) -> None:
        """Handle pause button click."""
        try:
            self.is_paused = not self.is_paused
            
            if self.is_paused:
                self.pause_button.icon = ft.Icons.PLAY_ARROW
                self.pause_button.tooltip = "Resume recommendations"
                self.current_recommendation_text.value = "Recommendations paused"
                self.current_recommendation_text.color = ft.Colors.GREY_600
            else:
                self.pause_button.icon = ft.Icons.PAUSE
                self.pause_button.tooltip = "Pause recommendations"
                self.current_recommendation_text.value = "Recommendations resumed"
                self.current_recommendation_text.color = ft.Colors.BLUE
            
            logger.info(f"Recommendations {'paused' if self.is_paused else 'resumed'}")
            
        except Exception as ex:
            logger.error(f"Failed to toggle pause: {ex}")
    
    def _on_break_clicked(self, e) -> None:
        """Handle take break button click."""
        try:
            self.current_recommendation_text.value = "Great! Take a 2-minute break and stretch your body."
            self.current_recommendation_text.color = ft.Colors.GREEN
            
            # Could integrate with a break timer here
            logger.info("User initiated break")
            
        except Exception as ex:
            logger.error(f"Failed to handle break action: {ex}")
    
    def _on_adjust_clicked(self, e) -> None:
        """Handle adjust posture button click."""
        try:
            adjustments = [
                "Sit up straight with shoulders back",
                "Keep feet flat on the floor",
                "Adjust monitor to eye level",
                "Relax shoulders and keep elbows at 90°",
                "Take deep breaths and reset your position"
            ]
            
            import random
            adjustment = random.choice(adjustments)
            self.current_recommendation_text.value = f"Posture tip: {adjustment}"
            self.current_recommendation_text.color = ft.Colors.BLUE
            
            logger.info("User requested posture adjustment")
            
        except Exception as ex:
            logger.error(f"Failed to handle adjust action: {ex}")
    
    def get_stats(self) -> Dict[str, Any]:
        """Get panel statistics.
        
        Returns:
            Dictionary containing panel statistics
        """
        return {
            'total_recommendations': self.recommendation_count,
            'current_risk_level': self.current_risk_level.value,
            'is_paused': self.is_paused,
            'history_count': len(self.recommendations_history),
            'last_recommendation_time': self.last_recommendation_time
        }
    
    def set_configuration(self, config: Dict[str, Any]) -> None:
        """Set panel configuration.
        
        Args:
            config: Configuration dictionary
        """
        if 'show_confidence' in config:
            self.show_confidence = config['show_confidence']
        
        if 'auto_clear_old' in config:
            self.auto_clear_old = config['auto_clear_old']
        
        if 'max_recommendations' in config:
            self.max_recommendations = config['max_recommendations']
            self.recommendations_history = deque(
                list(self.recommendations_history)[:self.max_recommendations],
                maxlen=self.max_recommendations
            )