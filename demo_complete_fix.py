"""Complete Fix Demo - Solusi Lengkap untuk Video Feed + Overlay

Demo ini menggabungkan semua perbaikan:
1. Video feed yang tidak tertampil (layar hitam)
2. Overlay skeleton yang hanya menampilkan garis hitam
3. Heatmap overlay yang tidak terlihat
4. Layer stacking dan transparansi issues

Perbaikan yang diterapkan:
- Video base layer dengan encoding yang diperbaiki
- Overlay RGBA dengan transparansi yang benar
- Layer stacking order yang tepat
- Debug dan fallback mechanisms
- High quality encoding untuk semua komponen
"""

import logging
import sys
import time
import threading
from pathlib import Path
from typing import Optional, Dict, Any, Tuple
import numpy as np
import base64
import io

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('complete_fix_demo.log')
    ]
)

logger = logging.getLogger(__name__)

try:
    from PIL import Image, ImageDraw
    import flet as ft
    import cv2
    PIL_AVAILABLE = True
    CV2_AVAILABLE = True
except ImportError as e:
    logger.error(f"Required dependencies not available: {e}")
    sys.exit(1)


def apply_complete_fixes():
    """Apply all fixes for video feed and overlay rendering."""
    
    # 1. Fix VideoSurface for video feed issues
    def patch_video_surface():
        try:
            import src.presentation.gpu.video_surface as video_module
            OriginalVideoSurface = video_module.VideoSurface
            
            class CompleteFixedVideoSurface(OriginalVideoSurface):
                def __init__(self, width: int = 640, height: int = 480, test_mode: bool = False):
                    super().__init__(width, height, test_mode)
                    self.frame_count = 0
                    logger.info(f"CompleteFixedVideoSurface initialized: {width}x{height}")
                
                def get_control(self) -> ft.Control:
                    """Get control dengan layer stacking yang diperbaiki."""
                    if not self.image_control or not self.overlay_control:
                        raise RuntimeError("VideoSurface not properly initialized")
                    
                    # Video layer dengan background eksplisit
                    video_container = ft.Container(
                        content=self.image_control,
                        width=self.width,
                        height=self.height,
                        bgcolor=ft.Colors.BLACK,
                        border=ft.border.all(1, ft.Colors.GREY_400),
                    )
                    
                    # Overlay layer dengan transparansi
                    overlay_container = ft.Container(
                        content=self.overlay_control,
                        width=self.width,
                        height=self.height,
                        bgcolor=ft.Colors.TRANSPARENT,
                    )
                    
                    # Stack dengan order yang jelas
                    return ft.Stack([
                        video_container,    # Base video layer
                        overlay_container,  # Overlay layer di atas
                    ], width=self.width, height=self.height)
                
                def update_frame(self, frame: np.ndarray) -> bool:
                    """Update frame dengan encoding yang diperbaiki."""
                    try:
                        if frame is None or frame.size == 0:
                            return False
                        
                        self.frame_count += 1
                        
                        if self.test_mode:
                            return True
                        
                        # Pastikan format frame benar
                        if len(frame.shape) != 3 or frame.shape[2] != 3:
                            logger.error(f"Invalid frame format: {frame.shape}")
                            return False
                        
                        # Resize jika perlu
                        if frame.shape[:2] != (self.height, self.width):
                            frame = cv2.resize(frame, (self.width, self.height))
                        
                        # High quality JPEG encoding
                        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]
                        success, buffer = cv2.imencode('.jpg', frame, encode_params)
                        
                        if not success:
                            logger.error("JPEG encoding failed")
                            return False
                        
                        # Create data URL
                        img_base64 = base64.b64encode(buffer).decode('utf-8')
                        data_url = f"data:image/jpeg;base64,{img_base64}"
                        
                        # Update image control
                        if self.image_control:
                            self.image_control.src_base64 = data_url
                            
                            # Debug log untuk frame pertama
                            if self.frame_count <= 3:
                                logger.info(f"Video frame {self.frame_count} updated, size: {len(data_url)}")
                            
                            return True
                        
                        return False
                        
                    except Exception as e:
                        logger.error(f"Video frame update failed: {e}")
                        return False
            
            video_module.VideoSurface = CompleteFixedVideoSurface
            logger.info("VideoSurface patched with complete fix")
            
        except Exception as e:
            logger.error(f"Failed to patch VideoSurface: {e}")
    
    # 2. Fix Overlay Renderers
    def patch_overlay_renderers():
        try:
            import src.presentation.gpu.skeleton_renderer as skeleton_module
            import src.presentation.gpu.heatmap_layer as heatmap_module
            
            # Fixed Skeleton Renderer
            class CompleteFixedSkeletonRenderer:
                def __init__(self, overlay_control, width=640, height=480, test_mode=False):
                    self.overlay_control = overlay_control
                    self.width = width
                    self.height = height
                    self.test_mode = test_mode
                    self.current_risk_level = 'unknown'
                    
                    # Risk colors dengan alpha yang tepat
                    self.RISK_COLORS = {
                        'low': (0, 255, 0, 200),
                        'medium': (255, 255, 0, 200),
                        'high': (255, 165, 0, 200),
                        'critical': (255, 0, 0, 200),
                        'unknown': (128, 128, 128, 200)
                    }
                    
                    # Skeleton connections
                    self.SKELETON_CONNECTIONS = [
                        ('nose', 'left_shoulder'), ('nose', 'right_shoulder'),
                        ('left_shoulder', 'right_shoulder'),
                        ('left_shoulder', 'left_elbow'), ('left_elbow', 'left_wrist'),
                        ('right_shoulder', 'right_elbow'), ('right_elbow', 'right_wrist'),
                        ('left_shoulder', 'left_hip'), ('right_shoulder', 'right_hip'),
                        ('left_hip', 'right_hip'),
                        ('left_hip', 'left_knee'), ('left_knee', 'left_ankle'),
                        ('right_hip', 'right_knee'), ('right_knee', 'right_ankle'),
                    ]
                    
                    logger.info(f"CompleteFixedSkeletonRenderer initialized: {width}x{height}")
                
                def set_risk_level(self, risk_level):
                    self.current_risk_level = risk_level
                
                def render_skeleton(self, keypoints):
                    try:
                        if not keypoints or not self.overlay_control:
                            return False
                        
                        if self.test_mode:
                            return True
                        
                        # Create transparent RGBA overlay
                        overlay = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
                        draw = ImageDraw.Draw(overlay)
                        
                        # Get color for risk level
                        color = self.RISK_COLORS.get(self.current_risk_level, self.RISK_COLORS['unknown'])
                        
                        # Draw skeleton connections dengan outline
                        for start_point, end_point in self.SKELETON_CONNECTIONS:
                            if start_point in keypoints and end_point in keypoints:
                                start_pos = keypoints[start_point]
                                end_pos = keypoints[end_point]
                                
                                if (len(start_pos) >= 3 and len(end_pos) >= 3 and
                                    start_pos[2] > 0.5 and end_pos[2] > 0.5):
                                    
                                    x1 = int(start_pos[0] * self.width)
                                    y1 = int(start_pos[1] * self.height)
                                    x2 = int(end_pos[0] * self.width)
                                    y2 = int(end_pos[1] * self.height)
                                    
                                    # Black outline untuk kontras
                                    draw.line([(x1, y1), (x2, y2)], fill=(0, 0, 0, 255), width=5)
                                    # Colored line
                                    draw.line([(x1, y1), (x2, y2)], fill=color, width=3)
                        
                        # Draw keypoints sebagai circles
                        for point_name, position in keypoints.items():
                            if len(position) >= 3 and position[2] > 0.5:
                                x = int(position[0] * self.width)
                                y = int(position[1] * self.height)
                                
                                radius = 6
                                # Black outline
                                draw.ellipse([x-radius-1, y-radius-1, x+radius+1, y+radius+1], 
                                           fill=(0, 0, 0, 255))
                                # Colored fill
                                draw.ellipse([x-radius, y-radius, x+radius, y+radius], 
                                           fill=color)
                                # White center
                                draw.ellipse([x-2, y-2, x+2, y+2], 
                                           fill=(255, 255, 255, 255))
                        
                        # Convert to base64 PNG
                        buffer = io.BytesIO()
                        overlay.save(buffer, format='PNG', optimize=True)
                        img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                        data_url = f"data:image/png;base64,{img_base64}"
                        
                        # Update overlay control
                        self.overlay_control.src_base64 = data_url
                        return True
                        
                    except Exception as e:
                        logger.error(f"Skeleton rendering failed: {e}")
                        return False
                
                def clear_skeleton(self):
                    try:
                        if self.overlay_control:
                            # Clear dengan transparent image
                            transparent = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
                            buffer = io.BytesIO()
                            transparent.save(buffer, format='PNG')
                            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                            self.overlay_control.src_base64 = f"data:image/png;base64,{img_base64}"
                            return True
                    except Exception as e:
                        logger.error(f"Clear skeleton failed: {e}")
                    return False
            
            # Fixed Heatmap Layer
            class CompleteFixedHeatmapLayer:
                def __init__(self, overlay_control, width=640, height=480, test_mode=False):
                    self.overlay_control = overlay_control
                    self.width = width
                    self.height = height
                    self.test_mode = test_mode
                    self.enabled = True
                    logger.info(f"CompleteFixedHeatmapLayer initialized: {width}x{height}")
                
                def set_enabled(self, enabled):
                    self.enabled = enabled
                
                def render_stub(self):
                    if not self.enabled or self.test_mode:
                        return True
                    
                    try:
                        # Create demo heatmap dengan gradient
                        overlay = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
                        
                        # Create radial gradient
                        center_x, center_y = self.width // 2, self.height // 2
                        max_radius = min(self.width, self.height) // 4
                        
                        for radius in range(max_radius, 0, -3):
                            alpha = int((radius / max_radius) * 100)  # Fade effect
                            color = (255, 100, 100, alpha)  # Red heatmap
                            
                            draw = ImageDraw.Draw(overlay)
                            draw.ellipse([center_x - radius, center_y - radius,
                                        center_x + radius, center_y + radius],
                                       fill=color)
                        
                        # Convert to base64
                        buffer = io.BytesIO()
                        overlay.save(buffer, format='PNG', optimize=True)
                        img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                        data_url = f"data:image/png;base64,{img_base64}"
                        
                        self.overlay_control.src_base64 = data_url
                        return True
                        
                    except Exception as e:
                        logger.error(f"Heatmap rendering failed: {e}")
                        return False
                
                def render_heatmap(self, intensity_map):
                    return self.render_stub()  # Use stub for demo
                
                def clear_heatmap(self):
                    try:
                        if self.overlay_control:
                            transparent = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
                            buffer = io.BytesIO()
                            transparent.save(buffer, format='PNG')
                            img_base64 = base64.b64encode(buffer.getvalue()).decode('utf-8')
                            self.overlay_control.src_base64 = f"data:image/png;base64,{img_base64}"
                            return True
                    except Exception as e:
                        logger.error(f"Clear heatmap failed: {e}")
                    return False
            
            # Replace original classes
            skeleton_module.SkeletonRenderer = CompleteFixedSkeletonRenderer
            heatmap_module.HeatmapLayer = CompleteFixedHeatmapLayer
            logger.info("Overlay renderers patched with complete fix")
            
        except Exception as e:
            logger.error(f"Failed to patch overlay renderers: {e}")
    
    # 3. Fix Main App Video Display
    def patch_main_app():
        try:
            import src.presentation.__main__ as main_module
            original_update = main_module.ErgonomicsApp._update_video_display
            
            def complete_fixed_update_video_display(self, frame: np.ndarray) -> None:
                try:
                    if not self.video_surface or not self.is_running or not self.page:
                        return
                    
                    if frame is None or frame.size == 0:
                        return
                    
                    # Direct update dengan error handling
                    success = self.video_surface.update_frame(frame)
                    
                    # Update page jika berhasil
                    if success and self.page:
                        try:
                            self.page.update()
                        except Exception as e:
                            logger.debug(f"Page update failed: {e}")
                    
                except Exception as e:
                    logger.debug(f"Video display update error: {e}")
            
            main_module.ErgonomicsApp._update_video_display = complete_fixed_update_video_display
            logger.info("Main app video display patched with complete fix")
            
        except Exception as e:
            logger.error(f"Failed to patch main app: {e}")
    
    # Apply all patches
    patch_video_surface()
    patch_overlay_renderers()
    patch_main_app()


def demo_complete_fix():
    """Launch demo dengan semua perbaikan diterapkan."""
    logger.info("MEMULAI DEMO COMPLETE FIX")
    logger.info("=" * 60)
    logger.info("PERBAIKAN LENGKAP YANG DITERAPKAN:")
    logger.info("   1. Video feed encoding dan display")
    logger.info("   2. Overlay skeleton dengan transparansi RGBA")
    logger.info("   3. Heatmap overlay dengan gradient")
    logger.info("   4. Layer stacking order yang benar")
    logger.info("   5. Error handling dan fallback mechanisms")
    logger.info("   6. High quality encoding untuk semua komponen")
    logger.info("=" * 60)
    
    try:
        # Apply all fixes
        apply_complete_fixes()
        
        # Import and run main app
        import src.presentation.__main__ as main_module
        
        logger.info("MELUNCURKAN APLIKASI DENGAN COMPLETE FIX...")
        logger.info("   Video: High quality JPEG encoding (95%)")
        logger.info("   Overlay: RGBA PNG dengan transparansi")
        logger.info("   Stacking: Video base + overlay layers")
        logger.info("   Debug: Enabled untuk troubleshooting")
        logger.info("")
        
        main_module.main()
        
    except KeyboardInterrupt:
        logger.info("Demo stopped by user")
        
    except Exception as e:
        logger.error(f"Demo error: {e}")
        sys.exit(1)
    
    finally:
        logger.info("=" * 60)
        logger.info("COMPLETE FIX DEMO COMPLETED")
        logger.info("Video feed dan overlay issues should be resolved!")
        logger.info("=" * 60)


if __name__ == "__main__":
    logger.info("TESTING COMPLETE FIX COMPONENTS...")
    
    # Test video encoding
    try:
        test_frame = np.random.randint(0, 255, (480, 640, 3), dtype=np.uint8)
        encode_params = [cv2.IMWRITE_JPEG_QUALITY, 95]
        success, buffer = cv2.imencode('.jpg', test_frame, encode_params)
        
        if success:
            logger.info("Video encoding test: PASSED")
        else:
            logger.error("Video encoding test: FAILED")
    except Exception as e:
        logger.error(f"Video encoding test error: {e}")
    
    # Test overlay creation
    try:
        overlay = Image.new('RGBA', (640, 480), (0, 0, 0, 0))
        draw = ImageDraw.Draw(overlay)
        draw.ellipse([100, 100, 200, 200], fill=(255, 0, 0, 150))
        
        buffer = io.BytesIO()
        overlay.save(buffer, format='PNG')
        img_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        if len(img_base64) > 0:
            logger.info("Overlay creation test: PASSED")
        else:
            logger.error("Overlay creation test: FAILED")
    except Exception as e:
        logger.error(f"Overlay creation test error: {e}")
    
    logger.info("Starting complete fix demo...")
    demo_complete_fix()
