2025-06-16 08:39:00,975 - __main__ - INFO - FixedOverlayRenderer initialized: 640x480
2025-06-16 08:39:01,079 - __main__ - INFO - 
2025-06-16 08:39:01,081 - __main__ - INFO - ============================================================
2025-06-16 08:39:01,092 - __main__ - INFO - 
2025-06-16 08:39:01,099 - __main__ - INFO - ============================================================
2025-06-16 08:39:06,392 - __main__ - INFO -    Renderer: FixedOverlayRenderer
2025-06-16 08:39:06,392 - __main__ - INFO -    Debug Mode: Enabled
2025-06-16 08:39:06,393 - __main__ - INFO -    Transparency: RGBA PNG
2025-06-16 08:39:06,394 - __main__ - INFO -    Fallback: Error indicators
2025-06-16 08:39:06,395 - __main__ - INFO - 
2025-06-16 08:39:06,398 - flet - INFO - Assets path configured: C:\Users\<USER>\Downloads\tubes bio\assets
2025-06-16 08:39:06,401 - flet - INFO - Starting up TCP server on localhost:56936
2025-06-16 08:39:06,410 - flet - INFO - Flet app has started...
2025-06-16 08:39:06,415 - flet - INFO - App URL: tcp://localhost:56936
2025-06-16 08:39:06,415 - flet_desktop - INFO - Starting Flet View app...
2025-06-16 08:39:06,416 - flet_desktop - INFO - Looking for Flet executable at: C:\Users\<USER>\Downloads\tubes bio\biogacorwell2\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 08:39:06,416 - flet_desktop - INFO - Flet View found in: C:\Users\<USER>\Downloads\tubes bio\biogacorwell2\Lib\site-packages\flet_desktop\app\flet\flet.exe
2025-06-16 08:39:06,648 - flet - INFO - App session started
2025-06-16 08:39:08,963 - src.infrastructure.camera.windows_camera_adapter - INFO - Camera initialized: 640x480 @ 30.00003000003fps
2025-06-16 08:39:09,394 - src.presentation.__main__ - INFO - Camera adapter initialized successfully
2025-06-16 08:39:09,394 - src.infrastructure.pose.movenet_engine - INFO - MoveNet model not found at movenet_thunder_float16.tflite
2025-06-16 08:39:09,394 - src.infrastructure.pose.movenet_engine - INFO - TODO: Download model from TFHub - implement in production
2025-06-16 08:39:09,395 - src.presentation.__main__ - WARNING - MoveNet initialization failed, trying MediaPipe
2025-06-16 08:39:09,443 - src.infrastructure.pose.mediapipe_engine - INFO - MediaPipe engine initialized (complexity=1)
2025-06-16 08:39:09,444 - src.application.use_cases.session_management - INFO - Session management use case initialized
2025-06-16 08:39:09,445 - src.domain.recommendation.rule_engine - INFO - Rule engine initialized with recommendation templates
2025-06-16 08:39:09,445 - src.domain.recommendation.fuzzy_engine - INFO - Fuzzy logic engine initialized
2025-06-16 08:39:09,446 - src.domain.recommendation.reinforcement_agent - INFO - Reinforcement learning agent initialized (demo mode)
2025-06-16 08:39:09,446 - src.application.use_cases.performance_monitor - INFO - PerformanceMonitor initialized (target: 25.0 FPS)
2025-06-16 08:39:09,449 - src.presentation.ui.widgets.recommendations_panel - INFO - Recommendations panel initialized
2025-06-16 08:39:09,454 - src.presentation.ui.widgets.reporting_panel - INFO - Reporting panel initialized
2025-06-16 08:39:09,473 - src.presentation.gpu.video_surface - INFO - VideoSurface initialized: 640x480, test_mode=False
2025-06-16 08:39:09,474 - src.presentation.gpu.skeleton_renderer - INFO - SkeletonRenderer initialized: 640x480, test_mode=False
2025-06-16 08:39:09,474 - src.presentation.gpu.heatmap_layer - INFO - HeatmapLayer initialized: 640x480, test_mode=False
2025-06-16 08:39:09,488 - src.presentation.__main__ - INFO - Video preview started
2025-06-16 08:39:09,488 - src.presentation.__main__ - INFO - Ergonomic assessment application started successfully
2025-06-16 08:39:15,288 - src.application.use_cases.session_management - INFO - Started new session: c4a77626-8598-4f6c-9289-58f60f2372da - Sesi 08:39
2025-06-16 08:39:15,288 - src.presentation.__main__ - INFO - Started session: c4a77626-8598-4f6c-9289-58f60f2372da
2025-06-16 08:39:17,370 - src.application.use_cases.generate_recommendation - INFO - Recommendation generation service started
2025-06-16 08:39:17,682 - src.infrastructure.camera.windows_camera_adapter - WARNING - Failed to read frame from camera
2025-06-16 08:39:19,119 - src.infrastructure.camera.windows_camera_adapter - INFO - Camera initialized: 640x480 @ 30.00003000003fps
2025-06-16 08:39:19,582 - src.infrastructure.pose.mediapipe_engine - INFO - MediaPipe engine initialized (complexity=1)
2025-06-16 08:39:19,583 - src.application.use_cases.performance_monitor - INFO - Performance monitoring started
2025-06-16 08:39:19,586 - src.application.use_cases.capture_and_score - INFO - Async capture and score pipeline started at 25.0 FPS
2025-06-16 08:39:19,601 - src.presentation.__main__ - INFO - Real-time assessment started
2025-06-16 08:39:19,910 - src.presentation.gpu.encoding - INFO - TurboJPEG encoder initialized successfully (automatic DLL discovery)
2025-06-16 08:39:20,611 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 4.9 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:39:21,649 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 7.8 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:39:22,667 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 5.9 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:39:23,684 - src.application.use_cases.performance_monitor - WARNING - Performance below target: 8.2 FPS (Capture: 0.0, Infer: 0.0, Render: 0.0) (target: 25.0 FPS)
2025-06-16 08:39:24,056 - __main__ - INFO - ============================================================
2025-06-16 08:39:24,108 - __main__ - INFO - 
2025-06-16 08:39:24,110 - __main__ - INFO - ============================================================
