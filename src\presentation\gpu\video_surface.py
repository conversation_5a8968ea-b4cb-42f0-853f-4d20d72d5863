"""GPU-accelerated video surface for real-time frame display.

This module provides a wrapper around <PERSON>let's Canvas using Skia GPU surface
for efficient zero-copy video frame rendering.
"""

import logging
import time
from typing import Optional
import numpy as np
import flet as ft
from PIL import Image
import io
import base64
from .encoding import _encode_frame

logger = logging.getLogger(__name__)


class VideoSurface:
    """GPU-accelerated video surface using Flet Canvas and Skia.
    
    Provides efficient frame updates via memoryview and zero-copy operations
    where possible. Falls back to PIL for format conversion when needed.
    
    Attributes:
        width: Surface width in pixels
        height: Surface height in pixels
        canvas: Flet Canvas control for GPU rendering
    """
    
    def __init__(self, width: int = 640, height: int = 480, test_mode: bool = False):
        """Initialize video surface with specified dimensions.
        
        Args:
            width: Surface width in pixels
            height: Surface height in pixels
            test_mode: If True, skip expensive operations for testing
        """
        self.width = width
        self.height = height
        self.test_mode = test_mode
        self.canvas: Optional[ft.Canvas] = None
        self.image_control: Optional[ft.Image] = None
        if not test_mode:
            self._initialize_surface()
        
        logger.info(f"VideoSurface initialized: {width}x{height}, test_mode={test_mode}")
    
    def _initialize_surface(self) -> None:
        """Initialize the Flet Canvas and Image controls for GPU rendering."""
        # TODO-CODE: Implement Skia GPU surface when Flet supports direct GPU access
        # For now, use ft.Image for frame display with PIL conversion
        
        # Create a default black image as placeholder
        default_image = Image.new('RGB', (self.width, self.height), color='black')
        buffer = io.BytesIO()
        default_image.save(buffer, format='PNG')
        default_base64 = base64.b64encode(buffer.getvalue()).decode()
        
        self.image_control = ft.Image(
            src_base64=default_base64,
            width=self.width,
            height=self.height,
            fit=ft.ImageFit.CONTAIN,
        )
        
        # Overlay image for skeleton and heatmap rendering
        # Create a transparent overlay as placeholder
        transparent_image = Image.new('RGBA', (self.width, self.height), color=(0, 0, 0, 0))
        overlay_buffer = io.BytesIO()
        transparent_image.save(overlay_buffer, format='PNG')
        overlay_base64 = base64.b64encode(overlay_buffer.getvalue()).decode()
        
        self.overlay_control = ft.Image(
            src_base64=overlay_base64,
            width=self.width,
            height=self.height,
            fit=ft.ImageFit.CONTAIN,
        )
    
    def get_control(self) -> ft.Control:
        """Get the Flet control for adding to page layout.
        
        Returns:
            Flet control containing video surface and overlays
        """
        if not self.image_control or not self.overlay_control:
            raise RuntimeError("VideoSurface not properly initialized")
        
        # Stack image and overlay for layered rendering
        return ft.Stack([
            self.image_control,
            self.overlay_control,
        ], width=self.width, height=self.height)
    
    def update_frame(self, frame: np.ndarray) -> bool:
        """Update video surface with new frame data.
        
        Converts numpy array to display format using zero-copy memoryview
        where possible, falling back to PIL for format conversion.
        
        Args:
            frame: Video frame as numpy array (H, W, C) in BGR format
            
        Returns:
            True if frame was successfully updated, False otherwise
        """
        try:
            if frame is None or frame.size == 0:
                logger.warning("Empty frame provided to update_frame")
                return False
            
            # Fast path for testing - skip expensive operations
            if self.test_mode:
                logger.debug("VideoSurface.update_frame skipped (test_mode)")
                return True
            
            # Resize frame to surface dimensions if needed
            if frame.shape[:2] != (self.height, self.width):
                import cv2
                frame = cv2.resize(frame, (self.width, self.height))
            
            # Use direct JPEG encoding for better performance
            import cv2
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                # Encode JPEG directly from BGR frame (no conversion needed)
                _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                img_base64 = f"data:image/jpeg;base64,{base64.b64encode(buffer).decode()}"
            elif len(frame.shape) == 2:
                # Grayscale frame - use PIL for grayscale
                pil_image = Image.fromarray(frame, mode='L')
                img_base64 = _encode_frame(pil_image, self.test_mode)
            else:
                # Default handling - convert BGR to RGB for PIL
                frame_rgb = frame[:, :, [2, 1, 0]]
                pil_image = Image.fromarray(frame_rgb, mode='RGB')
                img_base64 = _encode_frame(pil_image, self.test_mode)
            
            if img_base64 is None:
                logger.error("Failed to encode frame in update_frame")
                return False
            
            # Update image control source
            if self.image_control:
                self.image_control.src_base64 = img_base64
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to update video frame: {e}")
            return False
    
    def clear_surface(self) -> None:
        """Clear the video surface and remove current frame."""
        try:
            if self.image_control:
                self.image_control.src_base64 = None
            if self.overlay_control:
                self.overlay_control.src_base64 = None
            logger.debug("Video surface cleared")
        except Exception as e:
            logger.error(f"Failed to clear video surface: {e}")
    
    def get_overlay_control(self) -> Optional[ft.Image]:
        """Get the overlay control for overlay rendering.
        
        Returns:
            Flet Image control for drawing overlays
        """
        if self.test_mode:
            # Return a mock control for testing
            return ft.Image(src_base64="", width=self.width, height=self.height)
        return self.overlay_control
    
    @staticmethod
    def from_ndarray(frame: np.ndarray, target_width: int = 640, target_height: int = 480, test_mode: bool = False) -> Optional[str]:
        """Convert numpy array to base64 string for fast Flet Image updates.
        
        This helper provides zero-copy conversion where possible for improved performance.
        
        Args:
            frame: Video frame as numpy array (H, W, C) in BGR format
            target_width: Target width for resizing (default: 640)
            target_height: Target height for resizing (default: 480)
            test_mode: If True, return dummy base64 for testing
            
        Returns:
            Base64-encoded PNG string or None if conversion failed
        """
        try:
            if frame is None or frame.size == 0:
                return None
            
            # Fast path for testing - return dummy base64
            if test_mode:
                logger.debug("VideoSurface.from_ndarray skipped (test_mode)")
                return "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
            
            # Resize frame if needed using optimized CV2
            if frame.shape[:2] != (target_height, target_width):
                import cv2
                frame = cv2.resize(frame, (target_width, target_height), interpolation=cv2.INTER_LINEAR)
            
            # Use JPEG encoding directly with BGR format (faster than PIL conversion)
            import cv2
            if len(frame.shape) == 3 and frame.shape[2] == 3:
                # Encode JPEG directly from BGR frame (no conversion needed)
                _, buffer = cv2.imencode('.jpg', frame, [cv2.IMWRITE_JPEG_QUALITY, 85])
                img_base64 = base64.b64encode(buffer).decode()
                return f"data:image/jpeg;base64,{img_base64}"
            elif len(frame.shape) == 2:
                # Grayscale frame
                pil_image = Image.fromarray(frame, mode='L')
            else:
                # Fallback to PIL for unusual formats
                frame_rgb = frame[:, :, [2, 1, 0]]  # BGR -> RGB
                pil_image = Image.fromarray(frame_rgb, mode='RGB')
            
            # Convert to base64 using shared encoder for non-BGR frames
            result = _encode_frame(pil_image, test_mode)
            return result
            
        except Exception as e:
            logger.error(f"Failed to convert ndarray to base64: {e}")
            return None


# TODO-CODE: Future GPU pipeline implementation
# class SkiaGPUSurface:
#     """Direct Skia GPU surface implementation for maximum performance.
#
#     Will be implemented when Flet provides direct GPU access or when
#     switching to a GPU-native framework like SDL2 + OpenGL.
#     """
#     pass