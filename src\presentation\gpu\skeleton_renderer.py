"""GPU-accelerated skeleton renderer for pose visualization.

This module provides rendering of joint lines and hand landmarks using
Skia path primitives with risk-level color coding.
"""

import logging
import time
from typing import Dict, List, Tuple, Optional
import flet as ft
from PIL import Image, ImageDraw
import io
import base64
import threading
from .encoding import _encode_frame
from ...core.events import ScoresUpdatedEvent
from ...domain.ergonomics.value_objects.score import RiskLevel

logger = logging.getLogger(__name__)


class SkeletonRenderer:
    """GPU-accelerated skeleton renderer using PIL drawing.
    
    Renders pose keypoints and connections with color coding based on
    ergonomic risk levels. Optimized for real-time performance.
    
    Attributes:
        overlay_control: Flet Image control for overlay display
        width: Overlay width in pixels
        height: Overlay height in pixels
        current_risk_level: Current risk level for color selection
    """
    
    # Pose skeleton connections (joint pairs)
    SKELETON_CONNECTIONS = [
        # Upper body
        ('left_shoulder', 'right_shoulder'),
        ('left_shoulder', 'left_elbow'),
        ('left_elbow', 'left_wrist'),
        ('right_shoulder', 'right_elbow'),
        ('right_elbow', 'right_wrist'),
        
        # Torso
        ('left_shoulder', 'left_hip'),
        ('right_shoulder', 'right_hip'),
        ('left_hip', 'right_hip'),
        
        # Lower body
        ('left_hip', 'left_knee'),
        ('left_knee', 'left_ankle'),
        ('right_hip', 'right_knee'),
        ('right_knee', 'right_ankle'),
        
        # Hand landmarks (when available)
        ('left_wrist', 'left_thumb'),
        ('left_wrist', 'left_index'),
        ('right_wrist', 'right_thumb'),
        ('right_wrist', 'right_index'),
    ]
    
    # Risk level color mapping (RGB tuples for PIL)
    RISK_COLORS = {
        RiskLevel.LOW: (76, 175, 80),      # Green
        RiskLevel.MEDIUM: (255, 193, 7),   # Yellow
        RiskLevel.HIGH: (255, 152, 0),     # Orange
        RiskLevel.CRITICAL: (244, 67, 54), # Red
    }
    
    def __init__(self, overlay_control: ft.Image, width: int = 640, height: int = 480, test_mode: bool = False):
        """Initialize skeleton renderer with overlay control and dimensions.
        
        Args:
            overlay_control: Flet Image control for overlay display
            width: Overlay width in pixels
            height: Overlay height in pixels
            test_mode: If True, skip expensive operations for testing
        """
        self.overlay_control = overlay_control
        self.width = width
        self.height = height
        self.test_mode = test_mode
        self.current_risk_level = RiskLevel.LOW
        self.confidence_threshold = 0.5
        
        # Performance optimizations
        self._render_lock = threading.Lock()
        self._last_keypoints_hash = None
        self._last_risk_level = None
        self._cached_overlay = None
        self._render_count = 0
        
        logger.info(f"SkeletonRenderer initialized: {width}x{height}, test_mode={test_mode}")
    
    def update_risk_level(self, scores_event: ScoresUpdatedEvent) -> None:
        """Update risk level from scores event for color selection.
        
        Args:
            scores_event: Event containing ergonomic scores and risk levels
        """
        try:
            # Use the highest risk level among all scores
            reba_risk = scores_event.scores.get_reba_risk_level()
            rula_risk = scores_event.scores.get_rula_risk_level()
            
            # Get highest risk level
            risks = [reba_risk, rula_risk]
            risk_values = [r.value for r in risks]
            max_risk_idx = max(range(len(risk_values)), key=lambda i: ['low', 'medium', 'high', 'critical'].index(risk_values[i]))
            
            self.current_risk_level = risks[max_risk_idx]
            logger.debug(f"Risk level updated to: {self.current_risk_level.value}")
            
        except Exception as e:
            logger.error(f"Failed to update risk level: {e}")
            self.current_risk_level = RiskLevel.LOW
    
    def draw(self, keypoints: Dict[str, Tuple[float, float, float]], risk_level: RiskLevel) -> bool:
        """Draw skeleton with specified risk level.
        
        Args:
            keypoints: Dictionary of joint positions (x, y, confidence)
            risk_level: Risk level for color coding
            
        Returns:
            True if skeleton was successfully drawn, False otherwise
        """
        self.current_risk_level = risk_level
        return self.render_skeleton(keypoints)
    
    def render_skeleton(self, keypoints: Dict[str, Tuple[float, float, float]]) -> bool:
        """Optimized skeleton rendering with caching and change detection.
        
        Args:
            keypoints: Dictionary of joint positions (x, y, confidence)
                       where x, y are normalized coordinates [0, 1]
        
        Returns:
            True if skeleton was successfully rendered, False otherwise
        """
        try:
            if not keypoints or not self.overlay_control:
                return False
            
            # Fast path for testing - skip expensive operations
            if self.test_mode:
                return True
            
            # Performance optimization: check if we need to re-render
            keypoints_hash = hash(tuple(sorted(keypoints.items())))
            if (self._last_keypoints_hash == keypoints_hash and
                self._last_risk_level == self.current_risk_level and
                self._cached_overlay is not None):
                # Use cached overlay if nothing changed
                self.overlay_control.src_base64 = self._cached_overlay
                return True
            
            # Non-blocking render attempt
            if not self._render_lock.acquire(blocking=False):
                # Skip this render if another is in progress (maintains FPS)
                return False
            
            try:
                self._render_count += 1
                
                # Create transparent overlay image
                overlay_image = Image.new('RGBA', (self.width, self.height), (0, 0, 0, 0))
                draw = ImageDraw.Draw(overlay_image)
                
                # Get color for current risk level
                skeleton_color = self.RISK_COLORS.get(self.current_risk_level, (128, 128, 128))
                
                # Render skeleton connections (optimized)
                self._render_connections_fast(draw, keypoints, skeleton_color)
                
                # Render keypoints (optimized)
                self._render_keypoints_fast(draw, keypoints, skeleton_color)
                
                # Convert to base64 using shared encoder
                img_base64 = _encode_frame(overlay_image, self.test_mode)
                if img_base64 is None:
                    return False
                
                # Update overlay control and cache
                self.overlay_control.src_base64 = img_base64
                self._cached_overlay = img_base64
                self._last_keypoints_hash = keypoints_hash
                self._last_risk_level = self.current_risk_level
                
                # Log performance every 100 renders
                if self._render_count % 100 == 0:
                    logger.debug(f"Skeleton renderer: {self._render_count} renders completed")
                
                return True
                
            finally:
                self._render_lock.release()
            
        except Exception as e:
            logger.error(f"Failed to render skeleton: {e}")
            return False
    
    def _render_connections_fast(self, draw: ImageDraw.ImageDraw, keypoints: Dict[str, Tuple[float, float, float]], color: Tuple[int, int, int]) -> None:
        """Optimized skeleton connections rendering.
        
        Args:
            draw: PIL ImageDraw object
            keypoints: Joint positions dictionary
            color: Line color as RGB tuple
        """
        # Pre-calculate pixel coordinates for all keypoints
        pixel_coords = {}
        for joint_name, (x, y, conf) in keypoints.items():
            if conf > self.confidence_threshold:
                pixel_coords[joint_name] = (int(x * self.width), int(y * self.height))
        
        # Batch draw connections
        for joint1, joint2 in self.SKELETON_CONNECTIONS:
            if joint1 in pixel_coords and joint2 in pixel_coords:
                px1, py1 = pixel_coords[joint1]
                px2, py2 = pixel_coords[joint2]
                draw.line([(px1, py1), (px2, py2)], fill=color, width=3)
    
    def _render_keypoints_fast(self, draw: ImageDraw.ImageDraw, keypoints: Dict[str, Tuple[float, float, float]], color: Tuple[int, int, int]) -> None:
        """Optimized keypoints rendering with batch operations.
        
        Args:
            draw: PIL ImageDraw object
            keypoints: Joint positions dictionary
            color: Circle color as RGB tuple
        """
        # Batch coordinates for better performance
        circles_outer = []
        circles_inner = []
        
        for joint_name, (x, y, conf) in keypoints.items():
            if conf > self.confidence_threshold:
                # Convert normalized coordinates to pixel coordinates
                px = int(x * self.width)
                py = int(y * self.height)
                
                # Prepare circle coordinates
                radius = 6
                inner_radius = 3
                
                circles_outer.append([(px - radius, py - radius), (px + radius, py + radius)])
                circles_inner.append([(px - inner_radius, py - inner_radius), (px + inner_radius, py + inner_radius)])
        
        # Batch draw outer circles
        for coords in circles_outer:
            draw.ellipse(coords, fill=color)
        
        # Batch draw inner circles
        for coords in circles_inner:
            draw.ellipse(coords, fill=(255, 255, 255))  # White

    def _render_connections(self, draw: ImageDraw.ImageDraw, keypoints: Dict[str, Tuple[float, float, float]], color: Tuple[int, int, int]) -> None:
        """Legacy method - kept for compatibility."""
        return self._render_connections_fast(draw, keypoints, color)
    
    def _render_keypoints(self, draw: ImageDraw.ImageDraw, keypoints: Dict[str, Tuple[float, float, float]], color: Tuple[int, int, int]) -> None:
        """Legacy method - kept for compatibility."""
        return self._render_keypoints_fast(draw, keypoints, color)
    
    def clear_skeleton(self) -> None:
        """Clear all skeleton rendering from overlay."""
        try:
            if self.overlay_control:
                self.overlay_control.src_base64 = None
            logger.debug("Skeleton cleared from overlay")
        except Exception as e:
            logger.error(f"Failed to clear skeleton: {e}")


# TODO-CODE: Future GPU shader implementation
# class SkiaGPUSkeletonRenderer:
#     """Direct GPU shader-based skeleton renderer for maximum performance.
#     
#     Will implement custom OpenGL/Vulkan shaders for:
#     - Instanced joint rendering
#     - GPU-based line anti-aliasing
#     - Real-time color interpolation based on risk levels
#     """
#     pass