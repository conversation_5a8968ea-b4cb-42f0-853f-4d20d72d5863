"""Enhanced MVP Demo - Report Generation and Real-Time Recommendations

This demo showcases the URGENT FEATURE ADD implementations:
1. Session summary report with statistics and insights
2. Real-time ergonomic recommendations based on REBA scores
3. Export functionality to CSV/JSON

Target completion: 1 hour for immediate demo enhancement.

NOTE: For overlay display fixes, use demo_fixed_overlay.py instead.
"""

import logging
import sys
import time
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent / "src"))

from src.presentation.__main__ import main

# Configure logging for demo
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('enhanced_mvp_demo.log')
    ]
)

logger = logging.getLogger(__name__)


def demo_enhanced_mvp():
    """Launch the enhanced MVP with new reporting and recommendation features."""
    logger.info("🚀 MEMULAI DEMO MVP YANG DITINGKATKAN")
    logger.info("=" * 60)
    logger.info("✅ FITUR 1: <PERSON><PERSON><PERSON> Sesi dengan Statistik")
    logger.info("   - W<PERSON>san sesi real-time")
    logger.info("   - Analisis statistik dan penilaian risiko")
    logger.info("   - Identifikasi periode risiko puncak")
    logger.info("   - Penilaian kualitas data")
    logger.info("")
    logger.info("✅ FITUR 2: Rekomendasi Ergonomik Real-Time")
    logger.info("   - Rekomendasi berbasis skor REBA langsung")
    logger.info("   - Indikator tingkat risiko dengan kode warna")
    logger.info("   - Pelacakan riwayat rekomendasi")
    logger.info("   - Tombol aksi cepat untuk respon segera")
    logger.info("")
    logger.info("✅ FITUR 3: Fungsi Ekspor")
    logger.info("   - Ekspor CSV dengan data sesi lengkap")
    logger.info("   - Ekspor JSON dengan laporan komprehensif")
    logger.info("   - Generasi laporan dengan wawasan yang dapat ditindaklanjuti")
    logger.info("")
    logger.info("=" * 60)
    logger.info("🎯 INSTRUKSI DEMO:")
    logger.info("1. Mulai sesi baru dengan nama yang deskriptif")
    logger.info("2. Mulai asesmen untuk melihat rekomendasi real-time")
    logger.info("3. Periksa tab 'Rekomendasi' untuk panduan langsung")
    logger.info("4. Pantau tab 'Laporan' untuk analitik sesi")
    logger.info("5. Gunakan tombol ekspor untuk menyimpan data sesi")
    logger.info("6. Coba postur yang berbeda untuk memicu berbagai rekomendasi")
    logger.info("")
    logger.info("📊 TATA LETAK UI BARU:")
    logger.info("   - Kiri: Feed video dan kontrol asesmen")
    logger.info("   - Tengah: Skor dan metrik performa")
    logger.info("   - Kanan: Panel tab dengan Rekomendasi dan Laporan")
    logger.info("")
    logger.info("⚡ FITUR REAL-TIME AKTIF:")
    logger.info("   - Statistik sesi yang disegarkan otomatis (interval 5 detik)")
    logger.info("   - Generasi rekomendasi langsung")
    logger.info("   - Pembaruan tingkat risiko instan")
    logger.info("   - Pemantauan kualitas data berkelanjutan")
    logger.info("=" * 60)
    
    try:
        # Log system info
        logger.info("🔧 INISIALISASI SISTEM:")
        logger.info("   - Arsitektur event bus: AKTIF")
        logger.info("   - Manajemen sesi: DIAKTIFKAN")
        logger.info("   - Engine rekomendasi: SIAP")
        logger.info("   - Generator laporan: DIINISIALISASI")
        logger.info("")
        
        # Launch the enhanced MVP application
        logger.info("🎬 MELUNCURKAN APLIKASI MVP YANG DITINGKATKAN...")
        logger.info("   Ukuran jendela: 1480x900 (dioptimalkan untuk tata letak baru)")
        logger.info("   Tema UI: Mode terang dengan indikator risiko berkode warna")
        logger.info("")
        
        main()
        
    except KeyboardInterrupt:
        logger.info("📋 DEMO SUMMARY:")
        logger.info("   Demo stopped by user")
        logger.info("   Enhanced MVP features demonstrated successfully")
        
    except Exception as e:
        logger.error(f"❌ DEMO ERROR: {e}")
        logger.info("📋 TROUBLESHOOTING:")
        logger.info("   - Check camera permissions and availability")
        logger.info("   - Ensure all dependencies are installed")
        logger.info("   - Verify pose detection engine initialization")
        sys.exit(1)
    
    finally:
        logger.info("=" * 60)
        logger.info("🎉 ENHANCED MVP DEMO COMPLETED")
        logger.info("✨ Key Features Successfully Implemented:")
        logger.info("   ✅ Session summary reports with comprehensive analytics")
        logger.info("   ✅ Real-time recommendations based on REBA scores")
        logger.info("   ✅ Export functionality (CSV/JSON) with full data")
        logger.info("   ✅ Enhanced UI with tabbed recommendations and reports")
        logger.info("   ✅ Auto-refresh and real-time insights")
        logger.info("   ✅ Risk level indicators and trend analysis")
        logger.info("")
        logger.info("🚀 READY FOR IMMEDIATE DEMO ENHANCEMENT!")
        logger.info("=" * 60)


def demo_feature_showcase():
    """Demonstrate key features of the enhanced MVP."""
    logger.info("🎯 FEATURE SHOWCASE MODE")
    logger.info("=" * 50)
    
    # Import enhanced components for standalone demo
    from src.application.use_cases.session_management import SessionManagementUseCase, SessionConfig
    from src.application.use_cases.report_generation import ReportGenerationUseCase
    from src.application.use_cases.generate_recommendation import GenerateRecommendationUseCase
    from src.core.event_bus import EventBus
    from src.domain.ergonomics.entities.assessment_session import AssessmentSession
    from src.domain.ergonomics.value_objects.score import ErgonomicScore
    from src.domain.ergonomics.entities.posture_snapshot import PostureSnapshot
    
    try:
        # Initialize core components
        event_bus = EventBus()
        session_config = SessionConfig(
            auto_save_interval=30.0,
            enable_real_time_analysis=True,
            recommendation_threshold=4.0
        )
        
        session_manager = SessionManagementUseCase(event_bus, session_config)
        report_generator = ReportGenerationUseCase()
        recommendation_engine = GenerateRecommendationUseCase(event_bus)
        
        logger.info("✅ Core components initialized")
        
        # Demo 1: Session Management
        logger.info("🔄 DEMO 1: Session Management")
        session = session_manager.start_session(
            session_name="Enhanced MVP Demo Session",
            participant_id="demo_user",
            assessment_type="continuous"
        )
        logger.info(f"   Created session: {session.session_id}")
        logger.info(f"   Session name: {session.session_name}")
        
        # Demo 2: Add sample data
        logger.info("📊 DEMO 2: Sample Data Generation")
        sample_keypoints = {
            'nose': (0.5, 0.2, 0.9),
            'left_shoulder': (0.3, 0.3, 0.9),
            'right_shoulder': (0.7, 0.3, 0.9),
            'left_hip': (0.4, 0.7, 0.9),
            'right_hip': (0.6, 0.7, 0.9),
        }
        
        # Simulate assessment data
        for i in range(10):
            # Create varying REBA scores for demonstration
            reba_score = 2 + (i * 1.2)  # Escalating risk
            rula_score = 1 + (i * 0.5)
            
            # Create posture snapshot
            posture = PostureSnapshot(
                timestamp=time.time() + i,
                keypoints=sample_keypoints,
                confidence=0.8 + (i * 0.02),
                metadata={'demo_frame': i}
            )
            
            # Create ergonomic score
            score = ErgonomicScore(
                reba_score=reba_score,
                rula_score=rula_score
            )
            
            # Add to session
            session_manager.add_posture_data(posture)
            session_manager.add_score_data(score)
            
            logger.info(f"   Added data point {i+1}: REBA={reba_score:.1f}, RULA={rula_score:.1f}")
            
            # Simulate some processing time
            time.sleep(0.1)
        
        # Demo 3: Generate Report
        logger.info("📈 DEMO 3: Report Generation")
        report = report_generator.generate_session_summary(session)
        
        logger.info("   Report Statistics:")
        stats = report['statistics']['overview']
        logger.info(f"   - Total Assessments: {stats['total_snapshots']}")
        logger.info(f"   - Average REBA Score: {stats['average_reba_score']:.1f}")
        logger.info(f"   - Peak REBA Score: {stats['peak_reba_score']:.1f}")
        logger.info(f"   - Average Posture Quality: {stats['average_posture_quality']:.1f}%")
        
        risk_analysis = report['risk_analysis']['overall_assessment']
        logger.info(f"   - Risk Level: {risk_analysis['average_risk_level'].upper()}")
        logger.info(f"   - Intervention Required: {risk_analysis['intervention_required']}")
        
        insights = report['insights']
        logger.info(f"   - Generated Insights: {len(insights)}")
        for insight in insights[:2]:  # Show first 2 insights
            logger.info(f"     * {insight['title']}: {insight['description']}")
        
        # Demo 4: Export Functionality
        logger.info("💾 DEMO 4: Export Functionality")
        
        # CSV Export
        csv_data = report_generator.export_to_csv(session)
        logger.info(f"   CSV Export: {len(csv_data)} characters generated")
        logger.info(f"   CSV Preview: {csv_data[:100]}...")
        
        # JSON Export
        json_data = report_generator.export_to_json(session, include_summary=True)
        logger.info(f"   JSON Export: {len(json_data)} characters generated")
        
        # Demo 5: Real-time Insights
        logger.info("⚡ DEMO 5: Real-Time Insights")
        insights = session_manager.get_real_time_insights()
        if insights:
            logger.info(f"   Current Risk Level: {insights['current_risk_level']}")
            logger.info(f"   Trend Direction: {insights['trend_direction']}")
            logger.info(f"   Recent Average Score: {insights['recent_average_score']}")
            logger.info(f"   Total Assessments: {insights['total_assessments']}")
            
            recommendations = insights.get('recommendations', [])
            if recommendations:
                logger.info(f"   Active Recommendations: {len(recommendations)}")
                for rec in recommendations:
                    logger.info(f"     * {rec['type'].upper()}: {rec['message']}")
        
        # Stop session
        session_manager.stop_session()
        logger.info("   Session completed successfully")
        
        logger.info("✅ ALL FEATURES DEMONSTRATED SUCCESSFULLY!")
        
    except Exception as e:
        logger.error(f"❌ Feature showcase error: {e}")
        raise


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--showcase":
        demo_feature_showcase()
    else:
        demo_enhanced_mvp()